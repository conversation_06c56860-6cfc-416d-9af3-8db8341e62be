-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create insurance_carriers table
CREATE TABLE IF NOT EXISTS insurance_carriers (
    id BIGSERIAL PRIMARY KEY,
    name VARCHA<PERSON>(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    website TEXT,
    contact_info JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(code)
);

-- Create procedures table
CREATE TABLE IF NOT EXISTS procedures (
    id BIGSERIAL PRIMARY KEY,
    cdt_code VARCHAR(10) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(cdt_code)
);

-- Create guidelines table
CREATE TABLE IF NOT EXISTS guidelines (
    id BIGSERIAL PRIMARY KEY,
    carrier_id BIGINT REFERENCES insurance_carriers(id),
    category VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    content JSONB NOT NULL,
    effective_date DATE,
    expiration_date DATE,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create carrier_procedure_requirements table
CREATE TABLE IF NOT EXISTS carrier_procedure_requirements (
    id BIGSERIAL PRIMARY KEY,
    carrier_id BIGINT REFERENCES insurance_carriers(id),
    procedure_id BIGINT REFERENCES procedures(id),
    requirements JSONB,
    documentation_needed JSONB,
    preauth_required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(carrier_id, procedure_id)
);

-- Create glossary_terms table
CREATE TABLE IF NOT EXISTS glossary_terms (
    id BIGSERIAL PRIMARY KEY,
    term VARCHAR(255) NOT NULL,
    definition TEXT NOT NULL,
    category VARCHAR(100),
    letter CHAR(1),
    related_terms TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(term)
);

-- Create documentation_requirements table
CREATE TABLE IF NOT EXISTS documentation_requirements (
    id BIGSERIAL PRIMARY KEY,
    carrier_id BIGINT REFERENCES insurance_carriers(id),
    document_type VARCHAR(100) NOT NULL,
    description TEXT,
    required_for JSONB,
    format_requirements TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create appeal_procedures table
CREATE TABLE IF NOT EXISTS appeal_procedures (
    id BIGSERIAL PRIMARY KEY,
    carrier_id BIGINT REFERENCES insurance_carriers(id),
    procedure_type VARCHAR(100) NOT NULL,
    steps JSONB,
    timeframes JSONB,
    contact_info JSONB,
    required_forms JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create embeddings table
CREATE TABLE IF NOT EXISTS embeddings (
    id BIGSERIAL PRIMARY KEY,
    content_type VARCHAR(50) NOT NULL,
    content_id BIGINT NOT NULL,
    embedding vector(1536),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create search_logs table
CREATE TABLE IF NOT EXISTS search_logs (
    id BIGSERIAL PRIMARY KEY,
    query_text TEXT NOT NULL,
    matched_content_ids JSONB,
    similarity_scores JSONB,
    user_feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_insurance_carriers_name_code ON insurance_carriers(name, code);
CREATE INDEX idx_procedures_cdt_code ON procedures(cdt_code);
CREATE INDEX idx_guidelines_carrier_category ON guidelines(carrier_id, category);
CREATE INDEX idx_glossary_terms_term ON glossary_terms(term);
CREATE INDEX idx_embeddings_content ON embeddings(content_type, content_id);
CREATE INDEX idx_embeddings_vector ON embeddings USING ivfflat (embedding vector_cosine_ops); 