import { Client, Pool } from 'pg';
import OpenAI from 'openai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Initialize OpenAI for embeddings
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Database connection configuration
const dbConfig = {
  connectionString: process.env.POSTGRES_CONNECTION_STRING,
  ssl: { rejectUnauthorized: false },
  connectionTimeoutMillis: 10000,
};

// Create a connection pool for better performance
const pool = new Pool(dbConfig);

// Types for database operations
export interface VectorSearchResult {
  id: number;
  content_type: string;
  content_id: number;
  similarity_score: number;
  metadata?: Record<string, any>;
  content?: string;
}

export interface GuidelineSearchResult {
  id: number;
  title: string;
  category: string;
  carrier: string;
  similarity: string;
  content: string;
}

export interface CarrierInfo {
  id: number;
  name: string;
  type?: string;
  payer_id?: string;
  claims_address?: string;
  phone_number?: string;
  website?: string;
  contact_info?: Record<string, any>;
}

export interface ProcedureInfo {
  code: string;
  name: string;
  description: string;
  category: string;
}

/**
 * Database utility class providing SDK-agnostic database operations
 */
export class DatabaseUtils {
  private static instance: DatabaseUtils;
  
  private constructor() {}
  
  public static getInstance(): DatabaseUtils {
    if (!DatabaseUtils.instance) {
      DatabaseUtils.instance = new DatabaseUtils();
    }
    return DatabaseUtils.instance;
  }

  /**
   * Create a new database client connection
   */
  public createClient(): Client {
    return new Client(dbConfig);
  }

  /**
   * Get the connection pool
   */
  public getPool(): Pool {
    return pool;
  }

  /**
   * Generate embedding for a given text using OpenAI
   */
  public async generateEmbedding(text: string): Promise<number[]> {
    try {
      const response = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: text,
      });
      
      return response.data[0].embedding;
    } catch (error) {
      throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Perform vector similarity search on embeddings table
   */
  public async vectorSearch(
    queryText: string,
    options: {
      contentType?: string;
      limit?: number;
      similarityThreshold?: number;
      carrierFilter?: string;
      categoryFilter?: string;
    } = {}
  ): Promise<VectorSearchResult[]> {
    const {
      contentType = 'guideline',
      limit = 5,
      similarityThreshold = 0.1,
      carrierFilter,
      categoryFilter
    } = options;

    const client = this.createClient();
    
    try {
      await client.connect();
      
      // Generate embedding for query
      const queryEmbedding = await this.generateEmbedding(queryText);
      
      // Build the query with optional filters
      let query = `
        SELECT 
          e.id,
          e.content_type,
          e.content_id,
          e.metadata,
          1 - (e.embedding <=> $1::vector) as similarity_score
        FROM embeddings e
        WHERE e.content_type = $2
          AND (1 - (e.embedding <=> $1::vector)) >= $3
      `;
      
      const params: any[] = [JSON.stringify(queryEmbedding), contentType, similarityThreshold];
      let paramIndex = 4;
      
      // Add carrier filter if specified
      if (carrierFilter) {
        query += ` AND (e.metadata->>'carrier_id')::bigint IN (
          SELECT id FROM insurance_carriers WHERE carrier_name ILIKE $${paramIndex}
        )`;
        params.push(`%${carrierFilter}%`);
        paramIndex++;
      }
      
      // Add category filter if specified
      if (categoryFilter) {
        query += ` AND e.metadata->>'category' ILIKE $${paramIndex}`;
        params.push(`%${categoryFilter}%`);
        paramIndex++;
      }
      
      query += `
        ORDER BY e.embedding <=> $1::vector
        LIMIT $${paramIndex}
      `;
      params.push(limit);
      
      const result = await client.query(query, params);
      
      return result.rows.map(row => ({
        id: row.id,
        content_type: row.content_type,
        content_id: row.content_id,
        similarity_score: row.similarity_score,
        metadata: row.metadata
      }));
      
    } finally {
      await client.end();
    }
  }

  /**
   * Search guidelines with detailed information including carrier names
   */
  public async searchGuidelines(
    queryText: string,
    options: {
      carrier?: string;
      category?: string;
      limit?: number;
    } = {}
  ): Promise<GuidelineSearchResult[]> {
    const { carrier, category, limit = 5 } = options;
    
    const client = this.createClient();
    
    try {
      await client.connect();
      
      // Generate embedding for query
      const queryEmbedding = await this.generateEmbedding(queryText);
      
      // Build comprehensive query with joins
      let query = `
        SELECT 
          g.id,
          g.title,
          g.category,
          ic.carrier_name as carrier,
          1 - (e.embedding <=> $1::vector) as similarity_score,
          CASE 
            WHEN jsonb_typeof(g.content) = 'string' THEN g.content::text
            ELSE g.content->>'text'
          END as content
        FROM embeddings e
        JOIN guidelines g ON e.content_id = g.id AND e.content_type = 'guideline'
        LEFT JOIN insurance_carriers ic ON g.carrier_id = ic.id
        WHERE 1=1
      `;
      
      const params: any[] = [JSON.stringify(queryEmbedding)];
      let paramIndex = 2;
      
      // Add carrier filter
      if (carrier) {
        query += ` AND ic.carrier_name ILIKE $${paramIndex}`;
        params.push(`%${carrier}%`);
        paramIndex++;
      }
      
      // Add category filter
      if (category) {
        query += ` AND g.category ILIKE $${paramIndex}`;
        params.push(`%${category}%`);
        paramIndex++;
      }
      
      query += `
        ORDER BY e.embedding <=> $1::vector
        LIMIT $${paramIndex}
      `;
      params.push(limit);
      
      const result = await client.query(query, params);
      
      return result.rows.map(row => ({
        id: row.id,
        title: row.title,
        category: row.category,
        carrier: row.carrier || 'Unknown',
        similarity: (row.similarity_score * 100).toFixed(1),
        content: row.content ? row.content.substring(0, 1000) + (row.content.length > 1000 ? '...' : '') : ''
      }));
      
    } finally {
      await client.end();
    }
  }

  /**
   * Look up procedure information
   */
  public async lookupProcedure(procedureQuery: string): Promise<ProcedureInfo[]> {
    const client = this.createClient();

    try {
      await client.connect();

      const query = `
        SELECT
          procedure_code as code,
          description as name,
          description,
          category
        FROM procedures
        WHERE
          LOWER(description) LIKE LOWER($1)
          OR LOWER(procedure_code) LIKE LOWER($1)
          OR LOWER(category) LIKE LOWER($1)
        ORDER BY
          CASE
            WHEN LOWER(procedure_code) = LOWER($2) THEN 1
            WHEN LOWER(description) = LOWER($2) THEN 2
            ELSE 3
          END
        LIMIT 5
      `;

      const result = await client.query(query, [`%${procedureQuery}%`, procedureQuery]);

      return result.rows.map(row => ({
        code: row.code,
        name: row.name,
        description: row.description,
        category: row.category
      }));

    } finally {
      await client.end();
    }
  }

  /**
   * Look up insurance carrier information
   */
  public async lookupCarrier(carrierName: string): Promise<CarrierInfo | null> {
    const client = this.createClient();
    
    try {
      await client.connect();
      
      const query = `
        SELECT 
          id,
          carrier_name as name,
          carrier_type as type,
          payer_id,
          claims_address,
          phone_number,
          website,
          contact_info
        FROM insurance_carriers
        WHERE carrier_name ILIKE $1
        ORDER BY 
          CASE WHEN carrier_name ILIKE $1 THEN 1 ELSE 2 END,
          carrier_name
        LIMIT 1
      `;
      
      const result = await client.query(query, [`%${carrierName}%`]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      const row = result.rows[0];
      return {
        id: row.id,
        name: row.name,
        type: row.type,
        payer_id: row.payer_id,
        claims_address: row.claims_address,
        phone_number: row.phone_number,
        website: row.website,
        contact_info: row.contact_info
      };
      
    } finally {
      await client.end();
    }
  }

  /**
   * Get database statistics
   */
  public async getDatabaseStats(): Promise<{
    totalGuidelines: number;
    totalEmbeddings: number;
    totalCarriers: number;
    vectorExtensionEnabled: boolean;
  }> {
    const client = this.createClient();
    
    try {
      await client.connect();
      
      // Get counts
      const guidelinesResult = await client.query('SELECT COUNT(*) as count FROM guidelines');
      const embeddingsResult = await client.query('SELECT COUNT(*) as count FROM embeddings');
      const carriersResult = await client.query('SELECT COUNT(*) as count FROM insurance_carriers');
      
      // Check vector extension
      const extensionResult = await client.query("SELECT extname FROM pg_extension WHERE extname = 'vector'");
      
      return {
        totalGuidelines: Number.parseInt(guidelinesResult.rows[0].count),
        totalEmbeddings: Number.parseInt(embeddingsResult.rows[0].count),
        totalCarriers: Number.parseInt(carriersResult.rows[0].count),
        vectorExtensionEnabled: extensionResult.rows.length > 0
      };
      
    } finally {
      await client.end();
    }
  }

  /**
   * Close the connection pool
   */
  public async closePool(): Promise<void> {
    await pool.end();
  }
}

// Export singleton instance
export const dbUtils = DatabaseUtils.getInstance();
