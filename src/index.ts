// Ensure we have valid API keys
import * as dotenv from 'dotenv';

// Load .env.development file
dotenv.config({ path: '.env.development' });

// Validate API key
if (!process.env.OPENAI_API_KEY) {
  console.error('ERROR: OPENAI_API_KEY is not set in .env.development');
  process.exit(1);
}

// Make sure it's available to openai library directly
process.env.AI_OPENAI_API_KEY = process.env.OPENAI_API_KEY;

import { dentalNarratorAgent, runDentalNarrator } from './agents/dental-narrator-agent';

async function main() {
  try {
    console.log('🦷 Dental Narrator Agent (OpenAI Agents SDK)');
    console.log('=' .repeat(50));
    console.log('✅ Agent initialized and ready to use!');
    console.log('📊 Database: 2,006 guidelines with vector search');
    console.log('🔍 Vector search: Optimized indexes (78ms avg)');

    console.log('\n📋 Available capabilities:');
    console.log('  • Generate professional dental narratives');
    console.log('  • Search insurance guidelines by carrier');
    console.log('  • Look up procedure codes and requirements');
    console.log('  • Provide carrier-specific documentation');

    console.log('\n🧪 Test the agent with:');
    console.log('  npm run test-openai-agent');

    console.log('\n💡 Example queries:');
    console.log('  "Generate a narrative for crown procedure on tooth #14 for Delta Dental"');
    console.log('  "What are the documentation requirements for root canal therapy?"');
    console.log('  "Find coverage guidelines for preventive care procedures"');

  } catch (error) {
    console.error('❌ Error starting the application:', error);
  }
}

main(); 