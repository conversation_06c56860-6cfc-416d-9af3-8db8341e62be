import { Client } from 'pg';
import OpenAI from 'openai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Initialize OpenAI for embeddings
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function testDatabaseConnection() {
  try {
    console.log('Testing Supabase pooler connection...');

    // Get the connection string
    const connectionString = process.env.POSTGRES_CONNECTION_STRING;
    if (!connectionString) {
      throw new Error('POSTGRES_CONNECTION_STRING environment variable is not set');
    }

    // Mask the password for logging
    const maskedConnectionString = connectionString.replace(/:[^:@]+@/, ':****@');
    console.log(`Connection string: ${maskedConnectionString}`);
    
    // Step 1: Basic connection test with native pg client
    console.log('\nStep 1: Testing basic PostgreSQL connection...');
    const client = new Client({
      connectionString,
      connectionTimeoutMillis: 10000,
      ssl: {
        rejectUnauthorized: false
      }
    });

    await client.connect();
    console.log('✓ Connection successful!');

    const versionResult = await client.query('SELECT version()');
    console.log(`✓ PostgreSQL version: ${versionResult.rows[0].version.split(',')[0]}`);

    // Step 2: Check vector extension
    console.log('\nStep 2: Testing vector extension...');
    const extensionResult = await client.query("SELECT extname FROM pg_extension WHERE extname = 'vector'");
    if (extensionResult.rows.length > 0) {
      console.log('✓ pgvector extension is installed');
    } else {
      console.log('❌ pgvector extension is not installed');
      await client.end();
      return;
    }

    // Step 3: Check embeddings table and data
    console.log('\nStep 3: Testing embeddings table...');
    const embeddingsCountResult = await client.query('SELECT COUNT(*) as count FROM embeddings');
    const embeddingsCount = Number.parseInt(embeddingsCountResult.rows[0].count);
    console.log(`✓ Found ${embeddingsCount} embeddings in database`);

    if (embeddingsCount > 0) {
      // Step 4: Test vector similarity search
      console.log('\nStep 4: Testing vector similarity search...');

      // Generate test embedding
      const embeddingResponse = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: 'dental insurance requirements for crowns',
      });

      const queryEmbedding = embeddingResponse.data[0].embedding;

      // Perform vector similarity search
      const vectorQuery = `
        SELECT
          e.id,
          e.content_type,
          e.content_id,
          e.metadata,
          1 - (e.embedding <=> $1::vector) as similarity_score
        FROM embeddings e
        WHERE e.content_type = 'guideline'
        ORDER BY e.embedding <=> $1::vector
        LIMIT 3
      `;

      const results = await client.query(vectorQuery, [JSON.stringify(queryEmbedding)]);

      console.log(`✓ Vector search successful! Retrieved ${results.rows.length} results`);
      if (results.rows.length > 0) {
        console.log('Sample results:');
        results.rows.forEach((result, i) => {
          const similarity = (result.similarity_score * 100).toFixed(1);
          console.log(`  [${i+1}] Similarity: ${similarity}% | Content ID: ${result.content_id}`);
        });
      }
    } else {
      console.log('No embeddings found. You may need to run: npm run generate-embeddings');
    }

    await client.end();
    
    console.log('\nDatabase connection test completed successfully!');
    
  } catch (error: unknown) {
    console.error('Database connection test failed:', error instanceof Error ? error.message : String(error));
  }
}

// Run the test
testDatabaseConnection().catch(console.error); 