import express from 'express';
import { DatabaseUtils } from '../utils/database';
import { z } from 'zod';

const router = express.Router();
const dbUtils = DatabaseUtils.getInstance();

// Request validation schemas
const CarrierQuerySchema = z.object({
  search: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});

const ProcedureQuerySchema = z.object({
  search: z.string().optional(),
  category: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});

const NetworkQuerySchema = z.object({
  carrier_id: z.number().optional(),
  state: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});

const AppealQuerySchema = z.object({
  carrier: z.string().optional(),
  procedure_type: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
});

// GET /carriers - Retrieve insurance carriers with optional filtering
router.get('/carriers', async (req, res) => {
  try {
    const query = CarrierQuerySchema.parse(req.query);
    const { search, limit, offset } = query;

    console.log(`🏥 Retrieving carriers | Search: ${search || 'All'} | Limit: ${limit} | Offset: ${offset}`);

    // Build SQL query
    let sqlQuery = `
      SELECT 
        id,
        name,
        code,
        website,
        contact_info,
        created_at,
        updated_at
      FROM insurance_carriers
    `;
    
    const params: any[] = [];
    let paramIndex = 1;

    if (search) {
      sqlQuery += ` WHERE (name ILIKE $${paramIndex} OR code ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    sqlQuery += ` ORDER BY name ASC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, offset);

    const client = dbUtils.createClient();
    await client.connect();

    try {
      const result = await client.query(sqlQuery, params);
      
      // Get total count for pagination
      let countQuery = 'SELECT COUNT(*) as total FROM insurance_carriers';
      const countParams: any[] = [];
      
      if (search) {
        countQuery += ' WHERE (name ILIKE $1 OR code ILIKE $1)';
        countParams.push(`%${search}%`);
      }
      
      const countResult = await client.query(countQuery, countParams);
      const total = parseInt(countResult.rows[0].total);

      res.json({
        success: true,
        results: result.rows,
        pagination: {
          total,
          limit,
          offset,
          has_more: offset + limit < total
        },
        metadata: {
          search_term: search || null,
          timestamp: new Date().toISOString()
        }
      });

    } finally {
      await client.end();
    }

  } catch (error) {
    console.error('❌ Carriers retrieval error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /procedures - Retrieve dental procedures with optional filtering
router.get('/procedures', async (req, res) => {
  try {
    const query = ProcedureQuerySchema.parse(req.query);
    const { search, category, limit, offset } = query;

    console.log(`🦷 Retrieving procedures | Search: ${search || 'All'} | Category: ${category || 'All'} | Limit: ${limit}`);

    // Build SQL query
    let sqlQuery = `
      SELECT 
        id,
        cdt_code,
        name,
        description,
        category,
        created_at,
        updated_at
      FROM procedures
      WHERE 1=1
    `;
    
    const params: any[] = [];
    let paramIndex = 1;

    if (search) {
      sqlQuery += ` AND (cdt_code ILIKE $${paramIndex} OR name ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
      params.push(`%${search}%`);
      paramIndex++;
    }

    if (category) {
      sqlQuery += ` AND category ILIKE $${paramIndex}`;
      params.push(`%${category}%`);
      paramIndex++;
    }

    sqlQuery += ` ORDER BY cdt_code ASC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, offset);

    const client = dbUtils.createClient();
    await client.connect();

    try {
      const result = await client.query(sqlQuery, params);
      
      // Get total count for pagination
      let countQuery = 'SELECT COUNT(*) as total FROM procedures WHERE 1=1';
      const countParams: any[] = [];
      let countParamIndex = 1;
      
      if (search) {
        countQuery += ` AND (cdt_code ILIKE $${countParamIndex} OR name ILIKE $${countParamIndex} OR description ILIKE $${countParamIndex})`;
        countParams.push(`%${search}%`);
        countParamIndex++;
      }
      
      if (category) {
        countQuery += ` AND category ILIKE $${countParamIndex}`;
        countParams.push(`%${category}%`);
      }
      
      const countResult = await client.query(countQuery, countParams);
      const total = parseInt(countResult.rows[0].total);

      res.json({
        success: true,
        results: result.rows,
        pagination: {
          total,
          limit,
          offset,
          has_more: offset + limit < total
        },
        metadata: {
          search_term: search || null,
          category_filter: category || null,
          timestamp: new Date().toISOString()
        }
      });

    } finally {
      await client.end();
    }

  } catch (error) {
    console.error('❌ Procedures retrieval error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /networks - Retrieve network information (placeholder for future implementation)
router.get('/networks', async (req, res) => {
  try {
    const query = NetworkQuerySchema.parse(req.query);
    
    // Placeholder response - this would be implemented when network data is available
    res.json({
      success: true,
      results: [],
      pagination: {
        total: 0,
        limit: query.limit,
        offset: query.offset,
        has_more: false
      },
      metadata: {
        message: 'Network data endpoint ready - awaiting network relationship implementation',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Networks retrieval error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /appeals - Retrieve appeal procedures with optional filtering
router.get('/appeals', async (req, res) => {
  try {
    const query = AppealQuerySchema.parse(req.query);
    const { carrier, procedure_type, limit, offset } = query;

    console.log(`⚖️ Retrieving appeals | Carrier: ${carrier || 'All'} | Type: ${procedure_type || 'All'}`);

    // Build SQL query
    let sqlQuery = `
      SELECT 
        ap.id,
        ap.procedure_type,
        ap.steps,
        ap.timeframes,
        ap.contact_info,
        ap.required_forms,
        ic.name as carrier_name,
        ap.created_at,
        ap.updated_at
      FROM appeal_procedures ap
      LEFT JOIN insurance_carriers ic ON ap.carrier_id = ic.id
      WHERE 1=1
    `;
    
    const params: any[] = [];
    let paramIndex = 1;

    if (carrier) {
      sqlQuery += ` AND ic.name ILIKE $${paramIndex}`;
      params.push(`%${carrier}%`);
      paramIndex++;
    }

    if (procedure_type) {
      sqlQuery += ` AND ap.procedure_type ILIKE $${paramIndex}`;
      params.push(`%${procedure_type}%`);
      paramIndex++;
    }

    sqlQuery += ` ORDER BY ic.name ASC, ap.procedure_type ASC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    params.push(limit, offset);

    const client = dbUtils.createClient();
    await client.connect();

    try {
      const result = await client.query(sqlQuery, params);
      
      // Get total count for pagination
      let countQuery = `
        SELECT COUNT(*) as total 
        FROM appeal_procedures ap
        LEFT JOIN insurance_carriers ic ON ap.carrier_id = ic.id
        WHERE 1=1
      `;
      const countParams: any[] = [];
      let countParamIndex = 1;
      
      if (carrier) {
        countQuery += ` AND ic.name ILIKE $${countParamIndex}`;
        countParams.push(`%${carrier}%`);
        countParamIndex++;
      }
      
      if (procedure_type) {
        countQuery += ` AND ap.procedure_type ILIKE $${countParamIndex}`;
        countParams.push(`%${procedure_type}%`);
      }
      
      const countResult = await client.query(countQuery, countParams);
      const total = parseInt(countResult.rows[0].total);

      res.json({
        success: true,
        results: result.rows,
        pagination: {
          total,
          limit,
          offset,
          has_more: offset + limit < total
        },
        metadata: {
          carrier_filter: carrier || null,
          procedure_type_filter: procedure_type || null,
          timestamp: new Date().toISOString()
        }
      });

    } finally {
      await client.end();
    }

  } catch (error) {
    console.error('❌ Appeals retrieval error:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as dataEndpoints };
