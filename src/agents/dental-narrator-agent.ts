import { Agent, tool, run } from '@openai/agents';
import { z } from 'zod';
import * as dotenv from 'dotenv';
import { dbUtils } from '../utils/database';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Tool: Search dental guidelines using vector similarity
const searchGuidelinesTool = tool({
  name: 'search_dental_guidelines',
  description: 'Search for relevant dental insurance guidelines using semantic similarity. Use this when you need specific coverage information, procedure requirements, or documentation guidelines.',
  parameters: z.object({
    query: z.string().describe('The search query describing what dental information is needed'),
    carrier: z.string().nullable().optional().describe('Specific insurance carrier name (e.g., "Delta Dental", "Cigna")'),
    category: z.string().nullable().optional().describe('Procedure category (e.g., "restorative", "preventive", "endodontic")'),
    limit: z.number().default(5).describe('Maximum number of guidelines to return')
  }),
  execute: async ({ query, carrier, category, limit }) => {
    console.log(`🔍 Searching guidelines: "${query}" | Carrier: ${carrier || 'Any'} | Category: ${category || 'Any'}`);
    
    try {
      const guidelines = await dbUtils.searchGuidelines(query, {
        carrier: carrier || undefined,
        category: category || undefined,
        limit: limit || 5
      });

      console.log(`✅ Found ${guidelines.length} relevant guidelines`);

      return JSON.stringify({
        query,
        results_count: guidelines.length,
        guidelines: guidelines
      }, null, 2);

    } catch (error) {
      console.error('❌ Error searching guidelines:', error);
      return `Error searching guidelines: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
});

// Tool: Get procedure information
const getProcedureInfoTool = tool({
  name: 'get_procedure_info',
  description: 'Get detailed information about dental procedures including CDT codes, descriptions, and categories.',
  parameters: z.object({
    procedure: z.string().describe('Procedure name or CDT code (e.g., "crown", "D2750", "root canal")'),
  }),
  execute: async ({ procedure }) => {
    console.log(`🦷 Looking up procedure: "${procedure}"`);

    try {
      const procedures = await dbUtils.lookupProcedure(procedure);

      if (procedures.length === 0) {
        return `No procedures found matching "${procedure}". Try searching with different terms.`;
      }

      console.log(`✅ Found ${procedures.length} matching procedures`);

      return JSON.stringify({
        search_term: procedure,
        procedures: procedures
      }, null, 2);

    } catch (error) {
      console.error('❌ Error looking up procedure:', error);
      return `Error looking up procedure: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
});

// Tool: Get insurance carrier information
const getCarrierInfoTool = tool({
  name: 'get_carrier_info',
  description: 'Get information about insurance carriers including contact details and coverage specifics.',
  parameters: z.object({
    carrier: z.string().describe('Insurance carrier name (e.g., "Delta Dental", "Cigna", "Aetna")'),
  }),
  execute: async ({ carrier }) => {
    console.log(`🏥 Looking up carrier: "${carrier}"`);

    try {
      const carrierInfo = await dbUtils.lookupCarrier(carrier);

      if (!carrierInfo) {
        return `No insurance carriers found matching "${carrier}". Try searching with different terms.`;
      }

      console.log('✅ Found carrier information');

      return JSON.stringify({
        search_term: carrier,
        carrier: {
          name: carrierInfo.name,
          payer_id: carrierInfo.payer_id,
          website: carrierInfo.website,
          contact_info: carrierInfo.contact_info,
          claims_address: carrierInfo.claims_address,
          phone_number: carrierInfo.phone_number
        }
      }, null, 2);

    } catch (error) {
      console.error('❌ Error looking up carrier:', error);
      return `Error looking up carrier: ${error instanceof Error ? error.message : String(error)}`;
    }
  },
});

// Create the Dental Narrator Agent
export const dentalNarratorAgent = new Agent({
  name: 'Dental Narrator Agent',
  instructions: `You are a professional dental narrative generator and insurance documentation specialist. Your role is to help dental professionals create accurate, comprehensive narratives for insurance claims and documentation.

Key Responsibilities:
1. **Generate Professional Narratives**: Create detailed, medically accurate dental narratives based on chart notes and clinical findings
2. **Insurance Guidance**: Provide specific coverage information and requirements for different insurance carriers
3. **Procedure Documentation**: Help with proper documentation requirements for various dental procedures
4. **CDT Code Assistance**: Provide accurate CDT codes and procedure descriptions

Guidelines:
- Always use proper dental terminology and medical language
- Include relevant clinical findings and treatment rationale
- Specify insurance carrier requirements when applicable
- Ensure narratives are suitable for insurance submission
- Be thorough but concise in documentation
- Use the search tools to find specific guidelines and requirements

When generating narratives:
1. First search for relevant guidelines using the carrier and procedure information
2. Include patient presentation, clinical findings, treatment provided, and prognosis
3. Justify medical necessity when required
4. Follow insurance-specific documentation requirements
5. Use appropriate CDT codes and procedure descriptions`,
  
  tools: [
    searchGuidelinesTool,
    getProcedureInfoTool,
    getCarrierInfoTool
  ],
});

// Helper function to run the agent
export async function runDentalNarrator(userInput: string) {
  console.log('🤖 Starting Dental Narrator Agent...');
  
  try {
    const result = await run(dentalNarratorAgent, userInput);
    return result.finalOutput;
  } catch (error) {
    console.error('❌ Error running dental narrator:', error);
    throw error;
  }
}

// Export for use in other modules
export { searchGuidelinesTool, getProcedureInfoTool, getCarrierInfoTool };
