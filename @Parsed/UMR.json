{"provider_name": "UMR", "processed_date": "2025-03-21T18:28:50.487495", "total_documents": 3, "documents": [{"filename": "UMR Faqs.pdf", "metadata": {"/CreationDate": "D:20240927104741-04'00'", "/Creator": "Adobe InDesign 19.5 (Macintosh)", "/ModDate": "D:20240927104743-04'00'", "/Producer": "Adobe PDF Library 17.0", "/Trapped": "/False"}, "total_pages": 5, "pages": [{"page_number": 1, "content": "Who is UMR?\nUMR is an affiliate of UnitedHealthcare and is \nnot an insurance company. UMR is a third-party \nadministrator (TPA) and is hired by employer groups \n(customers) to administer and process the plan \nbenefits determined by the customer group. Self-\nfunded employer groups (customers) fund their \nclaims. TPAs allow for greater flexibility with benefit \nadministration, as well as access to a number of \nprovider networks that fit the needs of the customer. \nThat said, no two customer plans are alike. UMR \nmaintains a separate claims platform with dedicated \ncustomer service for our customers.\nWhat is UHSS?\nUnitedHealthcare Shared Services, otherwise known \nas UHSS, is a product that UMR assists in obtaining \nUnitedHealthcare network discounts and provider \nservices. UHSS is neither an insurance company nor \na TPA. Although the claim address used for UHSS is \nthe same as UMR, the ID card does not contain UMR’s \ninformation or logo. Please use the number of the \nUHSS ID card when calling for information. A separate \nFAQ is available for UHSS.\nWhat are the timely filing requirements  \nfor UMR?\nTimely filing requirements are determined by  \nthe self-funded customer, as well as the provider-\ncontracted timely filing provisions. The claim must be \nfiled within the provider’s timely filing limits or it may \nbe denied. If the provider disputes a claim that was \ndenied due to timely filing, the provider must submit \nproof they filed the claim within the timely filing limits. \nTimely filing limits can vary based on the provider’s \ncontract and/or the self-funded benefit plan.\n \n \n  \nWhat are the major differences between  \ncustomer benefit plans serviced by UMR?\nUMR’s processes are basically the same, and as a \nTPA, we work to customize the health care needs \nof the customer. Differences would be in the types \nof services selected as part of the administration, \nthe level of benefits at which covered services are \nprocessed and the services covered. Examples of  \nservices available include medical and dental claim \nadministration, FSA, PBM, stop loss carriers, vision \nplans, case management and utilization management.\nCan providers access a list of UMR denial \ncodes and definitions?\nUMR uses the American National Standards Institute \n(ANSI) denial codes and definitions.\nWhat is the customer service number for \nphysicians, facilities and other health care \nprofessionals to call for eligibility, benefits \nand follow-up on claim issues?\nOur Interactive Voice Response (IVR) system number \nis ************. The IVR system offers information via \nfaxback. If the provider has additional questions, the \nfaxback contains a passcode and number to call to \nspeak with a representative.\nThe UMR web portal, umr.com, is a convenient \nresource for accessing claim information and also \nobtaining the phone number and passcode, which \nwill allow the provider to speak with a representative.UMR questions answered  \nfor providersFAQ"}, {"page_number": 2, "content": "How do providers working with UMR  \nmembers access online resources?\nSimply go to umr.com. On the first visit, providers \nwill need to register their tax identification numbers \n(TINs). This website is an efficient way to check claim \nstatus, obtain benefits and much more. Be sure all \nTINs used are registered. If the provider is having \ntrouble registering, they can contact Technical \nSupport at ************.\nNote: This is a different, secure website for UMR \nmember claim/benefit information.\nWhat types of forms are available on  \numr.com?\nThe following forms can be found on umr.com:\n• Prior authorization\n• Dental claim\n• Electronic remittance advice (ERA)\n• UMR post-service appeal request\n• Various clinical request forms\nIf the physician’s office only has a member’s \nSocial Security number, will the office be able \nto verify member information online?\nYes. They can search using the employee’s Social \nSecurity number, and the results will include the \nmember’s unique health care ID number. Due to \nHIPAA requirements, UMR will not show the Social \nSecurity number online in the information back to \nthe provider.\nHow does a provider obtain a passcode on \numr.com to speak with a representative?\n• Sign in to umr.com. \n• Enter in the member ID number. \n• Select the family member. \n• Select Summary view from drop-down. \n• Select Search.  \n•  Go to Need additional information on  \nthis member? \n• Select Provider service center and the  \npasscode will be provided.\nCall ************, follow the prompts and enter the \npasscode to speak with a representative.How many accounts can the customer service \nrepresentative handle during one call?\nGenerally, customer service will assist with three \nmember cases per call. However during high call \nvolume situations, the number of cases may be \nlimited to prevent extended hold times.\nCan customer service representatives make \nclaim adjustments over the phone?\nYes. UMR Customer First Representatives (CFRs) can \naddress claim adjustments over the phone, depending \non the claim details. CFRs cannot change a claim if \ninappropriate modifiers or CPT/HCPCS codes are \nlisted. Such issues require a resubmission of the claim \nwith corrected codes from the servicing provider. \nNote: CFRs cannot advise a provider on how to bill.\nHow do providers know what network  \nthe member uses?\nThe primary network(s) can be found on the ID card.\nHow can a provider confirm they are a \nparticipating provider in the network?\nBy visiting umr.com and going to Find a provider for \nverification.\n \n \n \n \n Headline here      2"}, {"page_number": 3, "content": "Will the member ID card specify what \nservices require notification?\nThe member ID cards do not specify what service \nrequires prior notification, however, the ID cards \ninclude information to begin the notification \nprocess. (See sample ID card, below.) The UMR CARE \ntelephone number is on the ID card. Use this number \nto initiate authorization/notification. \n \n \n \n \n \n \n \n \n \n \n \n \n \n \n \n \n \n \n \n \nWhat services will require prior notification \nfor UMR customers?\nServices requiring prior notification or prior \nauthorization are identified by the member’s self-\nfunded benefit plan document. Member penalties \nmay be applicable for these services, as defined in the \nplan document for the specific customer group.\nProviders can view member-specific requirements at \numr.com within the patient search feature. Simply \nselect the Prior authorization from the horizontal \nnavigation bar and enter the appropriate criteria.  The \nsearch tool will display whether Prior authorization \nor Medical Necessity Review apply, as well as when \nthere is no coverage for the service.Where can a provider submit a pre-\ndetermination?\nPlease contact the benefit department via the \nphone number on the member’s ID card for benefits \non the procedure they are inquiring about. A pre-\ndetermination does not guarantee benefits. The \nbenefit department would advise level of coverage \nor if service is non-covered within the plan the \nmember uses.\nTo check if predetermination is recommended and \nsubmit predetermination, please visit umr.com.\nWhat is the claims mailing address?\nThe address is located on the member’s ID card.\nWhen a claim is in processing, how  \ncan a provider check status?\nCheck  claim status by visiting umr.com or by calling \n************.\nIf a payment is not received, how can a  \nprovider request a check tracer?\nUMR can initiate the check tracer process 30 days \nafter the check was issued. After the check tracer has \nbeen initiated, UMR works with the employer group \nto verify if the check has been cashed. This process \ncan take up to an additional 30 days.\nHow should a corrected claim be submitted?\nHCFA/Professional claims:\nResubmission code 7 in box 22.\nUB/Facility claims: \nBill type XX7 in box 4.  \nThe ‘7’ indicates corrected claim.\nNote: If submitting on a paper claim, stamping \n“corrected claim” is highly suggested.\n \n \n \n \n \n Headline here      3\nBack\nFront"}, {"page_number": 4, "content": "A claim was denied for medical records.  \nWhat are the next steps?\nReturn the letter request with the medical records. \nThis will ensure the records are routed to the correct \ndepartment for review and prevent any potential \ndelays. Do not resubmit the original claim with the \nmedical records.\nAt UMR, claims are denied for additional information \n(not pended).\nMedical records can be submitted using the following \nthree options:\nOnline: Refer to online portal at umr.com \nFax: Use the fax number noted on the  \nrequest letter.\nMail: Use mailing address noted on the request letter.\nWhere can records on CD be sent?\nUMR  \nPO Box 8042 \nWausau, WI 54402-8042\nWhat should a provider do if a claim denied \nas a duplicate to a Medicaid claim?\nMedicaid is responsible to bill UMR for \nreimbursement of what was previously paid  \nto the provider.\nIf Medicaid returns the UMR payment, UMR  \ncan reconsider the provider’s claim at that time.  \nUMR customer plan provisions will apply.\nHow does a provider appeal a benefit denial?\nSend via the following options:\nOnline: umr.com\nAppeals Address: \nUMR- Claim Appeals \nPO Box 30546 \nSalt Lake City, UT 84130\n \n \n \n \n \n What address can refund checks be sent to?\nUMR \nATTN-Adjustment Team \nPO Box 8033 \nWausau, WI 54402\nAlong with the refund check, submit any \ndocumentation to support the reason for the \nrefund as well as information to identify the claim(s) \ninvolved. (UMR member ID, patient name, date of \nservice, UMR claim number, and/or refund letter \nrequest if applicable.)\nWhere can a provider go on the umr.com \nwebsite to display refund tracking?\nSign in to umr.com. Select Refund tracking  \nunder myMenu. Enter Financial Control  \nNumber (FCN).\nAll FCN’s must be 11 digits long. The FCN is located \non the remittance advice.\nIf a provider did not receive a remit with the \npaper check, what should they do?\n• Sign in to umr.com. \n• Select Advanced claims from the Claims  \ndrop-down. \n• Select Check number. Enter the 10 digit  \ncheck number. \n• Enter the Group number and select Search.\nThe results will show all claims paid on the given \ncheck. Providers can call the customer service \nnumber on the back of the ID card to request a copy \nor the remit sent to them.\nWhat happens if a provider switched  \ndelivery from paper to electronic or wants to \nkeep both options?\nUMR will keep the provider on dual delivery of both \nfor six months.\nIf the provider would like to stop the dual delivery, \nSign in to umr.com. Select Provider. Select Find \na form and select the electronic paper remittance \nadvices request form.Headline here      4"}, {"page_number": 5, "content": "How does a provider enroll for an 835?\nContact OptumInsight\nPhone: ************, option 1\nWebsite: optumprovider.optum.com\nHow does the Electronic Funds Transfer (EFT) \nand Electronic Remittance Advice (ERA)  \nprocess work?\nEFT enrollment does not guarantee that all payments \ncoming from UMR will be sent using this electronic \noption. EFT approval must also be received from UMR \ncustomer groups. UMR is a third-party administrator \npaying claims from each customer’s bank account.\nNote: There is no charge to the provider to enroll in \nthe EFT/ERA process.\nWhen UMR processes a claim, the check/EFT issue \ndate will determine the date that the funds are sent \nto the electronic vendor. The electronic vendor will \nmake a deposit into the provider’s account. Please \nnote that this is typically 3-7 days after UMR sends \nthe funds to the electronic vendor.\nImportant: The customer chooses which day of the \nweek/month to release payment.How does a provider enroll in the ERA?\nEnroll by using UMR Payor ID 39026, unless  \nnoted differently.\nHow does a provider contact the EFT vendor?\nEFT Vendor – Optum Financial Health: \nHelp Desk: ************\nwww.optumbank.com \nEFT Vendor – Zelis:\nHelp Desk: ************\nwww.zelispayments.com\nHave the claim number and Tax ID ready for \nquestions on an EFT deposit.\n© 2024 United HealthCare Services, Inc.  UM1491  0924\nNo part of this document may be reproduced without permission.Headline here      5"}]}, {"filename": "UMR Preauth Guide.pdf", "metadata": {"/CreationDate": "D:**************-05'00'", "/Creator": "Adobe InDesign 19.5 (Macintosh)", "/ModDate": "D:**************-06'00'", "/Producer": "Adobe PDF Library 17.0", "/Trapped": "/False"}, "total_pages": 19, "pages": [{"page_number": 1, "content": "Prior authorization requirement \nsearch and submission tool\nA guide for external users"}, {"page_number": 2, "content": "Table of contents\nAccessing the tool                                                                           3\nSearching for members                                                                    3\nNavigating the patient search page                                                   4\nHow to view cases for a specific patient                                              5\nHow to view decision history                                                            5\nHow to view the Dashboard                                                              6\nNavigating the Dashboard                                                                6\nDashboard actions                                                                          7\nPerform a new search\nEnter requirement search criteria                                                      7\nPerform requirement search                                                             8\nInterpreting requirement search results \nBasic information                                                                            9\nColor-coded page sections                                                               9\nTier tabs                                                                                      10\nInterpreting prior authorization search results\nCode-based and conditional requirements                                       11 \nOther conditional requirements                                                      11\nDecision ID                                                                                   12\nSubmitting a request\nEnter required information–treatment type and date of service          12\nServices required section                                                               13\nProvider information section                                                          14\nProvider information section–provider not found                              14\nFollow-up contact information                                                        15\nSupporting documentation section                                                 15\nSubmitting                                                                                   16\nTIN validation                                                                               16\nTransaction number                                                                      17\nHow to submit a pre-service appeal request                                      17\nHow to identify a case with an existing appeal                                  19"}, {"page_number": 3, "content": "Prior authorization requirement search and submission tool   l        3\nThe purpose of UMR’s prior authorization requirement and submission tool is to answer the \nquestion, “Is a prior auth required or a pre determination recommended for this member, \nfor this service, on this date, performed by this provider?”\nOur online prior authorization requirement \nsearch tool allows you to access plan-specific \nrequirements for services. Specifically, the tool will:\n• Display whether prior-authorization or medical \nnecessity review apply\n• Provide other criteria that might be applicable \nto the prior authorization requirements such as \nplace-of-service criteria\n \n If further action is required (such as a prior \nauthorization), the tool will capture the \ninformation entered in the initial requirement \nsearch, then prompt the user to enter any \nadditional information required to process the \nrequest, such as:\n• Treatment type\n• Length of request \n• Additional procedure or diagnosis codes\n• Clinical documentation\nAccessing the tool\n1    Go to umr.com and select \nProvider at the top of the page.\n2    Select Sign in\n3    Sign in with your One \nHealthcare ID\nSearching for members\nAfter signing in, go to Patient \nsearch. Enter the subscriber ID or \nSocial Security number. Then select \nSearch.\n(Fictionalized data)"}, {"page_number": 4, "content": "Prior authorization requirement search and submission tool   l        4\nAfter finding the patient/member \nyou’re looking for, select Prior \nauthorization from the View   \ndrop-down, then select Search.\nNavigating the patient \nsearch page\nAfter selecting the patient, you can:\n• View case status – view the \nstatus of submitted cases, \nupdate existing cases, or \nfinish draft (previously saved) \ntransactions\n• Go to Decision history – using \na Decision ID, retrieve the results \nof a previous requirement search\n• Visit my dashboard – check the \nstatus of a case submitted for \nany member\nScroll down the page to the Prior \nauthorization search section \nto enter criteria to begin a new \nrequirement search.\n(Fictionalized data)\n"}, {"page_number": 5, "content": "Prior authorization requirement search and submission tool   l        5\nHow to view cases for a \nspecific patient\nSelect View case status to do the \nfollowing for a specific member:\n• View the status of  \nsubmitted cases\n• Take action, or update  \nexisting cases\n• Finish previously saved \ntransactions\nSelect the calendar icon to populate \ndate of service or enter date using \nthe MM/DD/YYYY format. Select \nSearch to update results for the \ndesired date range.\nIn the Action column, you can \nedit your transaction, submit an \nextension, admission notification  \nor appeal.\nHow to view decision history\nSelect Decision history to find and \nview previous requirement search \nresults for a member.\nSelect family member and Select \ncategory from drop-down menus \nto narrow results, then select Apply. \nEnter Decision ID to further refine \nyour results.\n(Fictionalized data)"}, {"page_number": 6, "content": "Prior authorization requirement search and submission tool   l        6\nHow to view the dashboard\nThe dashboard allows a user to \nsearch for existing cases or drafts for \nany patient and take the following \nactions:\n• View the status of  \nsubmitted cases\n• Update existing cases\n• Finish previously saved \ntransactions\nThere are two ways  to navigate  \nto the dashboard:  \nMethod 1: Select View my \ndashboard on the patient  \nsearch screen.\nMethod 2: Select Prior \nauthorization from the  \nnavigation bar.\nNavigating the dashboard\nSearch by case status or date of \nservice.\nEnter Transaction number to search \nfor an existing case or draft. \nSelect the service date range to view \na list of cases you have started or \ncompleted.\n(Fictionalized data)"}, {"page_number": 7, "content": "Prior authorization requirement search and submission tool   l        7\nDashboard actions\nSelect the links in the Actions \ncolumn to complete desired actions \nfor the case.\nPerform a new search:  \nEnter requirement  \nsearch criteria \nUnder Prior authorization search, \nthe following are required\n(marked with an asterisk):\n• Place of service\n• Procedure code or description \n(HCPCS, CPT, or description of \nservice). Note: Up to 5 codes \ncan be entered during the initial \nrequirement search\n• Diagnosis code or description\n• Date of service (use today’s date \nif unknown)\n• Rendering provider TIN\nWhile diagnosis code and TIN are not\nrequired, it is highly recommended\nfor precise results.\nNote: Diagnosis codes must include\na decimal point after the 3rd \ncharacter, XXX.XX.\n(Fictionalized data)"}, {"page_number": 8, "content": "Prior authorization requirement search and submission tool   l        8\nPerform requirement search\nWhen the desired search criteria \nhave been entered, review the details \nfor accuracy and modify fields as \nnecessary.\nNote: If applicable, additional \nprocedure and diagnosis codes can \nbe entered during the submission \nprocess. \nWhen you are satisfied, select Search \nto view results.\n(Fictionalized data)"}, {"page_number": 9, "content": "Prior authorization requirement search and submission tool   l        9\nInterpreting requirement \nsearch results:  \nBasic information \nBased on the search criteria entered, \nthe tool will display one or more of \nthe following options:\n• Prior authorization is required\n• Medical necessity review needed/\npre-determination recommended\n• No coverage for this service\n• No procedure code requirements \nfound\nThe requirements displayed on the \nPrior authorization search results page \nwill be listed by section with results \nfurther separated by Tier, if applicable. \nResults related to the procedure \ncode will be displayed near the top, \nfollowed by other factors that may \napply, such as place of service, etc.\nThe next few pages will further \nexplain how to interpret results within \neach section.\nInterpreting requirement  \nsearch results:   \nColor-coded page sections\nIt is important to scroll through all \nresults to determine which apply \nto the situation. Color-coded page \nsections indicate the following:\n(Fictionalized data)"}, {"page_number": 10, "content": "Prior authorization requirement search and submission tool   l        10\nInterpreting requirement \nsearch results:  \nTier tabs \nWhen provider criteria is included \nin the requirement search, the \nresults displayed will default to the \nappropriate Tier tab related to that \nprovider. \nNote: If no provider is included in  \nthe requirement search, the results \nwill default to the In-network, or  \nTier 1 tab.\nTo view requirements for other tiers, \nselect the desired tab.\n(Fictionalized data)"}, {"page_number": 11, "content": "Prior authorization requirement search and submission tool   l        11\nInterpreting prior authorization  \nsearch results:  \nCode-based and conditional requirements \nYou may also see the result:  \nNo requirements found for this procedure code.\nSee important additional requirements that may \napply below.\nThis means that the procedure code entered is \nnot found in the member’s prior authorization \nrequirements, but other Important additional \nrequirements are shown further down the page  \nthat may apply. Important: The Important additional \nrequirements section should be evaluated any time \nit is displayed. \nThis section displays requirements that may be \nrelevant, other than those based the CPT code. \nNote: Including a diagnosis code in the initial search \nor searching by the diagnosis code field may narrow \ndown some of the information displayed in this \nsection. When a combination of results are returned, \nfollow the chart below. \nInterpreting prior \nauthorization search results:  \nOther conditional \nrequirements \nSome results are dependent on factors \nsuch as patient age, dollar amount or \nvisit threshold.\nIn some instances, a call to customer \nservice will be suggested.\n(Fictionalized data)"}, {"page_number": 12, "content": "Prior authorization requirement search and submission tool   l        12\nInterpreting prior \nauthorization search results: \nDecision ID\nA Decision ID is displayed after each \nrequirement search. When the search \nproduces a result that indicates \nno further action is required, the \nDecision ID should be stored for \nyour records. If requirements apply, \nusers will select the appropriate \naction button to navigate to a screen \nwhere additional information will \nbe collected to process the request.  \nA new Decision ID number will \nbe provided when submission is \ncomplete.  \nSubmitting a request:  \nEnter required information – \ntreatment type and date  \nof service\nAfter selecting either Prior \nauthorization submission, Request \npre-determination, or Complete \nnotification buttons, users will be \nprompted to enter any additional \ninformation required to complete \nthe request or notification.\nNote: All search criteria entered as \npart of the requirement search will \nauto-populate within the form.\nAs shown here, users will be required \nto select a Treatment type from a \ndrop-down list based on the chosen \nPlace of service. The Service end \ndate can be entered by either \nselecting the calendar, navigating to \nand selecting the correct date, or by \ntyping the date directly in the field \nusing the MM/DD/YYYY format.  \n(Fictionalized data)"}, {"page_number": 13, "content": "Prior authorization requirement search and submission tool   l        13\nSubmitting a request: Services requested section\nAny procedure code or diagnosis code can be deleted from the submission form by selecting <PERSON><PERSON><PERSON> on the \nleft-hand side of each grid.Procedure code(s)\nAdditional codes not included within the requirement \nsearch can be added to the request or notification by \nusing the Enter procedure code field. \nNote: Be sure to enter values for any boxes that \nare displayed – ex: modifier, units, type of units, \nestimated bill amount.Diagnosis code(s)   \nAdditional diagnosis codes can be added by using \nthe free-form text field labeled Type a diagnosis \ncode or description. \nNote: Be sure to use the check box in the Primary \ncolumn to select the appropriate primary diagnosis \ncode for the request.\n(Fictionalized data)"}, {"page_number": 14, "content": "Prior authorization requirement search and submission tool   l        14\nSubmitting a request:  \nProvider information section\nWithin the Provider information \nsection, the rendering provider and \nfacility TIN may be required and will \nbe marked with a red asterisk. To \nsearch for the rendering provider \nor facility, enter a valid TIN in the \nrespective search field and select the \nSelect physician or Select facility \nbutton.  \nNote: The provider search within this \nsection has the same functionality \nas the search within the initial \nrequirement.\nSubmitting a request:  \nProvider information section \n– provider not found\nIf the correct search criteria have \nbeen entered, but the desired \nprovider is not included within the \nsearch results OR there are no results \nfound (like shown here), select Add \nprovider manually.\nIn the new window, enter all \nrequired information (as indicated \nwith an asterisk) and select Search.In the example above, the facility was included in the requirement search but \nthe rendering provider is also required, so a TIN search to select the physician \nmust still be performed.  \n(Fictionalized data)"}, {"page_number": 15, "content": "Prior authorization requirement search and submission tool   l        15\nSubmitting a request:  \nFollow-up contact \ninformation\nIn the Follow-up contact \ninformation section, fill out the \nrequired fields for the individual \nwho can provide additional clinical \ninformation or details if needed.\nNote: All fields are required.\n• Name\n• Email address\n• Phone number\n• Fax number\nSubmitting a request:  \nSupporting documentation \nsection\nClinical information related to \nthe request should be entered in \nthe Supporting documentation \nsection. This can be done by \nselecting the Upload file button to \nattach an existing file, or by entering \nfree-form text in the Additional \ncomments field.\nNote: Users can access provider \nforms by selecting Access a list of \nforms here. When selected, the \nmenu to the right will display. \n(Fictionalized data)"}, {"page_number": 16, "content": "Prior authorization requirement search and submission tool   l        16\nSubmitting a request:  \nTIN validation\nAfter selecting Submit on the \nsubmission form, users may see \nthe following window. This usually \nhappens when a provider was \nmanually added and is not yet \nassociated with an existing provider \naccount. Simply select the TIN that \nis associated with your account and \nselect Submit to continue with the \nsubmission request.\nNote: A new TIN can permanently \nbe added to your provider profile by \nselecting TIN maintenance  from \nthe main navigation.\n(Fictionalized data)\nSubmitting a request: Submitting\nAt the bottom of the submission form, users can select action buttons.\nNote: When users select Submit, red error messaging may appear on the screen to identify missing or incorrect \nentries. The request cannot be submitted until all errors have been corrected."}, {"page_number": 17, "content": "Prior authorization requirement search and submission tool   l        17\nSubmitting a request:  \nTransaction number\nSubmitting a request will generate a \ntransaction number as shown here.  \nIt is important to keep this number \nfor your records. It will be needed to \nretrieve or modify the request.\nHow to submit a pre-service \nappeal request\nIn the Action column, you can  \nselect the link next to the Appeal \nicon to navigate to the Appeal \nrequest form.\nOn the next screen, select the \ncode(s) to be included in the appeal \nrequest by selecting the checkbox \nnext to each line.  If desired, include \ninformation related to the appeal \nrequest within the Reason for \nAppeal free-form text box.\nNote: There will be options for \nuploading supporting clinical \ndocumentation further down in  \nthe form.\nIn the Follow-up contact \ninformation section, fill out the \nrequired fields for the individual \nwho can provide additional clinical \ninformation or details if needed.\n(Fictionalized data)"}, {"page_number": 18, "content": "Prior authorization requirement search and submission tool   l        18\nThe Pre-Service Appeals – \nDesignation of Authorized \nRepresentative form is required to \nprocess the appeal request. It can \nbe accessed via the link shown here.  \nThe completed form, along with any \nother clinical documentation, can be \nattached to the request by using the \nUpload button on the form.\nA green check mark will appear to \nindicate that a file has successfully \nbeen uploaded to the request. When \nall information and supporting \ndocumentation have been entered \nin the form, select the Submit \nbutton.Pre-Service Appeals - Designation of  \nAuthorized Representative\nAcknowledgement\nI, ____________________________________ (name of Authorized Representative) , have read the above Designation  \nof Authorized Representative, and I hereby accept this designation and agree to act as Authorized Representative for  \n________________________________ (claimant’s name) with respect to the above defined claim.\nDate ______/______/__________\nSignature of Authorized Representative _________________________________________________________\nNotices may be sent to the Authorized Representative at the following address:\nName ________________________________________________________\nStreet Address ____________________________________ City _____________________ State ____ Zip __________\n© 2022 United Healthcare Solutions, Inc.  UMF0011  0422  UA115 W. Wausau Ave \nWausau, WI 54401-2875 \nI, ________________________________, (your name) do hereby appoint, \n__________________________________ (your Authorized Representative)  (hereinafter “my Authorized\nRepresentative”) to act on my behalf in pursuing a benefit claim, specifically, my claim(s) for  \n______________________________________________________ (insert pre-service case number)\nMy Authorized Representative shall have full authority to act and receive notices on my behalf with respect to an \ninitial determination of the claim, any request for documents relating to the claim, and any appeal of an adverse \nbenefit determination of the claim. \nI understand that in the absence of a contrary direction from me, UMR will direct all information and notices regarding \nthe claim to which I otherwise am entitled, including benefit determinations, to my Authorized Representative only. \nI am aware that the Standards for Privacy of Individually Identifiable Health Information set forth by the U.S. \nDepartment of Health and Human Services (the “Privacy Standards”) govern access to medical information.  \nI understand that in connection with the performance of his/her duties hereunder, my Authorized Representative  \nmay receive my Protected Health Information, as defined in the Privacy Standards, relating to the claim. I hereby \nconsent to any disclosure of my Protected Health Information to my Authorized representative.  \nDate ______/______/__________         Member ID ___________________________________________\nSignature of patient or patient’s guardian __________________________________________________UHC Appeals - UMR \nP .O. Box 400046 \nSan Antonio, TX 78229\nMM                DD                    YEAR\nMM                DD                    YEAR\n(Fictionalized data)"}, {"page_number": 19, "content": "Prior authorization requirement search and submission tool   l        19\nHow to identify a case with \nan existing appeal\nIn the Status column, you can track \nthe current station of a submitted \nappeal request.\n© 2024 United HealthCare Services, Inc     UM1927  1224   \nNo part of this document may be reproduced without permission    (Fictionalized data)"}]}, {"filename": "UMR Preauth v PredD.pdf", "metadata": {"/CreationDate": "D:20250128110152-05'00'", "/Creator": "Adobe InDesign 20.0 (Macintosh)", "/ModDate": "D:20250128142824-06'00'", "/Producer": "Adobe PDF Library 17.0", "/Trapped": "/False"}, "total_pages": 7, "pages": [{"page_number": 1, "content": "When it comes to verifying medical necessity before a procedure or service is \nrendered, we want to make sure you understand the difference in prior authorization \nand predetermination and how UMR manages both. You will also find information \non how to submit a prior authorization/predetermination request, as well as details on \nchecking the status of a request and a couple of frequently asked questions.Prior authorization vs. \npredetermination\nThis document provides instructions for how to search for prior authorization (PA) and predetermination \nrequirements, as well as appropriate methods for submitting requests.\nPrior authorization\nA requirement   \nof the patient’s employer plan that \nspecific services be reviewed for \nmedical necessity and authorized \nprior to them being rendered. Predetermination\nNot a requirement   \nof the patient’s employer plan. \nRather, UMR does this as a courtesy \nreview for the provider to determine \nif the services are medically necessary \nprior to them being rendered.  "}, {"page_number": 2, "content": "At UMR, we’ve created a new tool to easily identify if services require prior authorization on the \nmembers’ plans.   \nOur new Prior Authorization Requirement Search Tool will quickly and accurately perform a search \nand deliver results that let you know if a prior auth is necessary for a specific member. The tool also \nprovides other situational criteria that might be applicable to the prior auth requirements.\nSign in to umr.com to access \nPatient search.\n(Continued)Follow these steps to perform a search\nAfter signing in, enter Patient \nID or Social Security number \n(SSN), then select Search.\nGo to Select patient, select \nPrior Authorization from \nView  drop-down, then  \nselect Search. The UMR prior authorization requirement search tool –  \na guide for external users\n1\n2\n3\n(Fictionalized data)"}, {"page_number": 3, "content": "On the Prior authorization \nsearch screen, the fields marked \nwith a red asterisk are required \nentries. To return the most precise \nresults, it is best to enter as much \ninformation as possible in the \nremaining fields.\nField entry notes:\n• A diagnosis code must \ninclude a decimal after the \nthird character – for example, \n123.45\n• If the date is unknown,  \nenter today’s date\nTIN search:  Enter a TIN in the \nRendering provider tax ID number \n(TIN) box, and then select Select \nprovider.   \nFrom the Provider type drop-down \nmenu, select either Physician or \n<PERSON>, and then enter additional \nsearch criteria and select Search.  \nSelect a provider from the Provider \nname column, then select Next . If \nyou do not see the correct provider \nlisted in the results, you can modify \ncriteria and search again, or select \n<PERSON><PERSON> to return to the prior \nauthorization search main screen.\n4\n(Continued)(Fictionalized data)"}, {"page_number": 4, "content": "When you are finished entering \nsearch criteria, review details \nfor accuracy and modify any \nnecessary fields. Select Search \nto view results. \nViewing your search \nresults\nBased on search criteria entered, the \ntool will display one or more of the \nfollowing options:\n• No coverage for this service\n• Prior authorization is required\n• Medical necessity review needed/\npre-determination recommended\n• No requirements found for the \nprocedure code\nThe results will display similar to  \nwhat you see here, defaulting to the  \nIn-network tab if no provider is  \nselected. Note that multiple tiers may  \nbe applicable. To view tiers, including \nout-of-network benefits, select the \ndesired tab.\nTo view requirements for other tiers, \nselect the desired tab.  \n5\n(Continued)(Fictionalized data)"}, {"page_number": 5, "content": "It is important to scroll through all results and determine which apply to the situation. In addition, you may \nsee color-coded results as follows:\nFurther down the results page, you will see \nimportant additional requirements that may apply. \nThis displays other results that may apply based \non factors other than the CPT code. Some of this \ninformation can be narrowed down by including a \ndiagnosis code in the initial search, or by searching \nthe diagnosis code in the Filter your results field.\nFurthermore, you may see a combination of results returned. If you do, follow this chart:\nOutside of the search criteria available, \nother results may be dependent on \nadditional factors such as patient age, \ndollar or visit threshold etc.\nIf you have questions or need assistance with this tool, please call the customer service number \non the member’s ID card. (Fictionalized data)\n(Continued)"}, {"page_number": 6, "content": "(Continued)\nStatus types\nThe following are classifications or “types” of Status you will see listed. \n•   Submitted: UMR has received and request  \nwill be reviewed \n•  In Progress: Being routed to proper area\n•  Pending review: Case is being reviewed \n•  Not Required: No authorization required\n•   Not handled by CM: UMR doesn’t manage this \nmember’s plan’s care management (CM)\n•   Partially approved: Case was partially approved•   Call back required: UMR requires additional \ninformation; therefore, UMR will call the  \nprovider contact \n•  Approved: Services approved \n•  Denied: Services denied\n•   Case Voided: Reason would be given as to why  \ncase was voided at time of voidHow to check the status of \nyour submission \nAfter submitting prior authorization/\npredetermination requests, you can \ncheck the status of each one. Follow \nthese steps.\n1.   From the Provider home page, \nselect Prior authorization from the \nnavigation bar. \n2.   Select Search by transaction \nnumber. \n3.   Enter transaction number and  \nselect Search. \nThe prior authorizations and \npredeterminations for the patient \ninformation you entered under \nyour tax ID will be displayed. The \nStatus will be indicated for each \none listed like shown here.\n(Fictionalized data)"}, {"page_number": 7, "content": "© 2025 United HealthCare Services, Inc.    UM1839   0125 \nNo part of this document may be reproduced without permission.  By phone  \nCall ************ to speak to a \nrepresentative for both prior authorization/\npredeterminations. The UMR representative \nwill need the following information:  \n•   Patient’s ID number, name and date of birth\n•     Provider and facility name, address, and \ntelephone number\n•   CPT and diagnosis codes\n•   Date of service \n•    Contact at your office/facility and  \nphone number \n•     Documentation to support medical necessity  Other ways to submit \nyour request\nHow will the provider be notified of the result of \nthe review? \nFor approvals and/or denials, a UMR team member \nmay call your office, but a letter will be sent to the \napplicable doctor, facility and/or member. \nFor prior authorizations only:  \n• If denied, then the appeal/dispute process will \nbe outlined in the notification letter. \n• If the prior authorization cannot be reviewed \nfor some reason, you will be notified with an \nexplanation. \nHow many days should I expect to wait before \nservices are approved? \nPredeterminations and preauthoriztions can take up \nto 15 business days to complete. Frequently asked questions"}]}]}