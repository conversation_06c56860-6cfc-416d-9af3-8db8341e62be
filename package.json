{"name": "dental-narrator-beta", "version": "1.0.0", "description": "Dental insurance claim narrative generator using RAG", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "init": "ts-node src/index.ts", "test-db": "ts-node src/test-db-connection.ts", "import-csv": "ts-node scripts/import-csv-data.ts", "validate-data": "ts-node scripts/validate-clean-data.ts", "fix-data-quality": "ts-node scripts/fix-data-quality-issues.ts", "test-embeddings": "ts-node scripts/test-embeddings.ts", "generate-embeddings": "ts-node scripts/generate-embeddings.ts", "optimize-vector-indexes": "ts-node scripts/optimize-vector-indexes.ts", "optimize-vector-performance": "ts-node scripts/optimize-vector-performance.ts", "test-vector-search": "ts-node scripts/test-vector-search.ts", "test-frontend": "ts-node scripts/test-frontend-simulation.ts", "test-openai-agent": "ts-node scripts/test-openai-agent.ts", "test-system": "ts-node scripts/test-complete-system.ts", "seed-database": "ts-node scripts/seed-database.ts", "mcp": "mcp-docs-server"}, "keywords": ["dental", "insurance", "claims", "narrative", "rag"], "author": "", "license": "MIT", "dependencies": {"@ai-sdk/openai": "^1.0.0", "@openai/agents": "^0.0.10", "@types/pg": "^8.15.4", "ai": "^4.1.66", "dotenv": "^16.3.1", "openai": "^4.104.0", "pg": "^8.16.2", "supabase": "^2.19.7", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20.11.5", "nodemon": "^3.0.3", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}