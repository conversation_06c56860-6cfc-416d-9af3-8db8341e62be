#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface NetworkData {
  network_name: string;
  contact_phone?: string;
  contact_email?: string;
  resource_url?: string;
  notes?: string;
}

interface NetworkRelationship {
  network_name: string;
  carrier_name: string;
  effective_date?: string;
  termination_date?: string;
  special_notes?: string;
  verification_required?: boolean;
}

class NetworkRelationshipManager {
  private createClient(): Client {
    return new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING,
      ssl: { rejectUnauthorized: false },
    });
  }

  async run() {
    console.log('🌐 Populating network relationships...');

    try {
      await this.extractNetworksFromDocuments();
      await this.createNetworkRelationshipsFromExtractedData();

      console.log('✅ Network relationships populated successfully!');

    } catch (error) {
      console.error('❌ Error populating network relationships:', error);
      process.exit(1);
    }
  }

  private async extractNetworksFromDocuments() {
    console.log('📄 Extracting network information from parsed documents...');

    const fs = require('fs');
    const path = require('path');

    const extractedNetworks = new Map<string, {
      name: string;
      carrier: string;
      contact_info?: string;
      notes?: string;
    }>();

    try {
      const parsedDir = path.join(process.cwd(), '@Parsed');
      const files = fs.readdirSync(parsedDir);

      for (const file of files) {
        if (!file.endsWith('.json')) continue;

        const filePath = path.join(parsedDir, file);
        const content = fs.readFileSync(filePath, 'utf-8');
        const data = JSON.parse(content);

        const carrierName = data.provider_name || file.replace('.json', '');

        // Extract network mentions from document content
        if (data.documents) {
          for (const doc of data.documents) {
            if (doc.pages) {
              for (const page of doc.pages) {
                const text = page.content || '';

                // Look for specific network patterns with context
                this.extractNetworkMentions(text, carrierName, extractedNetworks);
              }
            }
          }
        }
      }

      console.log(`  📊 Extracted ${extractedNetworks.size} network references from documents:`);
      for (const [key, network] of extractedNetworks) {
        console.log(`    - ${network.name} (${network.carrier})`);
      }

      // Store extracted networks in database
      await this.storeExtractedNetworks(extractedNetworks);

    } catch (error) {
      console.log('  ⚠️  Error extracting from documents:', error);
    }
  }

  private extractNetworkMentions(text: string, carrierName: string, networks: Map<string, any>) {
    const lowerText = text.toLowerCase();

    // Delta Dental networks
    if (lowerText.includes('delta dental premier') || lowerText.includes('premier network')) {
      networks.set('Delta Dental Premier', {
        name: 'Delta Dental Premier',
        carrier: carrierName,
        notes: 'Traditional fee-for-service network'
      });
    }

    if (lowerText.includes('delta dental ppo') || lowerText.includes('ppo network')) {
      networks.set('Delta Dental PPO', {
        name: 'Delta Dental PPO',
        carrier: carrierName,
        notes: 'Preferred provider organization'
      });
    }

    if (lowerText.includes('deltacare usa') || lowerText.includes('dmo')) {
      networks.set('DeltaCare USA', {
        name: 'DeltaCare USA',
        carrier: carrierName,
        notes: 'Dental maintenance organization'
      });
    }

    // Extract phone numbers and contact info if present
    const phoneMatch = text.match(/(\d{3}[-.]?\d{3}[-.]?\d{4})/);
    const contactInfo = phoneMatch ? phoneMatch[0] : undefined;

    // Update existing entries with contact info
    for (const [key, network] of networks) {
      if (network.carrier === carrierName && contactInfo && !network.contact_info) {
        network.contact_info = contactInfo;
      }
    }
  }

  private async storeExtractedNetworks(networks: Map<string, any>) {
    const client = this.createClient();
    await client.connect();

    try {
      for (const [key, network] of networks) {
        // Check if network already exists
        const existingResult = await client.query(
          'SELECT id FROM insurance_networks WHERE network_name = $1',
          [network.name]
        );

        if (existingResult.rows.length === 0) {
          // Insert new network
          await client.query(`
            INSERT INTO insurance_networks (network_name, contact_phone, notes, created_at, updated_at)
            VALUES ($1, $2, $3, NOW(), NOW())
          `, [network.name, network.contact_info, network.notes]);
        } else {
          // Update existing network
          await client.query(`
            UPDATE insurance_networks
            SET contact_phone = COALESCE($2, contact_phone),
                notes = COALESCE($3, notes),
                updated_at = NOW()
            WHERE network_name = $1
          `, [network.name, network.contact_info, network.notes]);
        }
      }

      console.log(`  ✅ Stored ${networks.size} networks in database`);

    } finally {
      await client.end();
    }
  }



  private async createNetworkRelationshipsFromExtractedData() {
    console.log('🔗 Creating network-carrier relationships from extracted data...');

    const client = this.createClient();
    await client.connect();

    try {
      // Get all networks and carriers from database
      const networksResult = await client.query('SELECT id, network_name FROM insurance_networks');
      const carriersResult = await client.query('SELECT id, carrier_name FROM insurance_carriers');

      const networkIds = new Map<string, number>();
      const carrierIds = new Map<string, number>();

      for (const row of networksResult.rows) {
        networkIds.set(row.network_name, row.id);
      }

      for (const row of carriersResult.rows) {
        carrierIds.set(row.carrier_name, row.id);
      }

      // Create relationships based on network names and carrier patterns
      let insertedCount = 0;
      let skippedCount = 0;

      for (const [networkName, networkId] of networkIds) {
        // Find matching carriers based on network name patterns
        const matchingCarriers = this.findMatchingCarriers(networkName, carrierIds);

        for (const carrierId of matchingCarriers) {
          try {
            await client.query(`
              INSERT INTO network_carrier_relationships (
                network_id, carrier_id, effective_date, verification_required, created_at, updated_at
              )
              VALUES ($1, $2, CURRENT_DATE, false, NOW(), NOW())
              ON CONFLICT (network_id, carrier_id) DO UPDATE SET
                updated_at = NOW()
            `, [networkId, carrierId]);
            insertedCount++;
          } catch (error) {
            skippedCount++;
          }
        }
      }

      console.log(`  ✅ Created ${insertedCount} network relationships from extracted data, skipped ${skippedCount}`);

    } finally {
      await client.end();
    }
  }

  private findMatchingCarriers(networkName: string, carrierIds: Map<string, number>): number[] {
    const matches: number[] = [];
    const lowerNetworkName = networkName.toLowerCase();

    for (const [carrierName, carrierId] of carrierIds) {
      const lowerCarrierName = carrierName.toLowerCase();

      // Match Delta Dental networks to Delta Dental carriers
      if (lowerNetworkName.includes('delta') && lowerCarrierName.includes('delta')) {
        matches.push(carrierId);
      }

      // Match other network patterns to carriers
      if (lowerNetworkName.includes('cigna') && lowerCarrierName.includes('cigna')) {
        matches.push(carrierId);
      }

      if (lowerNetworkName.includes('aetna') && lowerCarrierName.includes('aetna')) {
        matches.push(carrierId);
      }

      if (lowerNetworkName.includes('humana') && lowerCarrierName.includes('humana')) {
        matches.push(carrierId);
      }

      if (lowerNetworkName.includes('united') && lowerCarrierName.includes('united')) {
        matches.push(carrierId);
      }

      if (lowerNetworkName.includes('guardian') && lowerCarrierName.includes('guardian')) {
        matches.push(carrierId);
      }

      if (lowerNetworkName.includes('blue cross') && lowerCarrierName.includes('blue cross')) {
        matches.push(carrierId);
      }

      if (lowerNetworkName.includes('anthem') && lowerCarrierName.includes('anthem')) {
        matches.push(carrierId);
      }
    }

    return matches;
  }
}

// Run the script
if (require.main === module) {
  const manager = new NetworkRelationshipManager();
  manager.run().catch(console.error);
}

export default NetworkRelationshipManager;
