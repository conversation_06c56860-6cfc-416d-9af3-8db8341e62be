#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface DocumentationRequirement {
  carrier_id?: number;
  document_type: string;
  description: string;
  required_for: string[];
  format_requirements?: string;
}

class DocumentationRequirementsExtractor {
  private createClient(): Client {
    return new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING,
      ssl: { rejectUnauthorized: false },
    });
  }

  async run() {
    console.log('📋 Extracting documentation requirements from documents...');
    
    try {
      await this.extractFromDocuments();
      
      console.log('✅ Documentation requirements extraction completed!');
      
    } catch (error) {
      console.error('❌ Error extracting documentation requirements:', error);
      process.exit(1);
    }
  }

  private async extractFromDocuments() {
    console.log('📄 Processing parsed documents for documentation requirements...');
    
    const parsedDir = path.join(process.cwd(), '@Parsed');
    
    if (!fs.existsSync(parsedDir)) {
      console.log('  ⚠️  @Parsed directory not found');
      return;
    }
    
    const files = fs.readdirSync(parsedDir);
    const allRequirements: DocumentationRequirement[] = [];
    
    for (const file of files) {
      if (!file.endsWith('.json')) continue;
      
      try {
        console.log(`  📄 Processing ${file}...`);
        
        const filePath = path.join(parsedDir, file);
        const content = fs.readFileSync(filePath, 'utf-8');
        const data = JSON.parse(content);
        
        const carrierName = this.extractCarrierName(file, data);
        const requirements = this.extractDocumentationRequirements(data, carrierName);
        
        allRequirements.push(...requirements);
        
        console.log(`    ✅ Extracted ${requirements.length} documentation requirements from ${file}`);
        
      } catch (error) {
        console.log(`    ❌ Error processing ${file}:`, error);
      }
    }
    
    console.log(`  📊 Total extracted requirements: ${allRequirements.length}`);
    
    // Store requirements in database
    await this.storeRequirements(allRequirements);
  }

  private extractCarrierName(filename: string, data: any): string {
    if (data.provider_name) {
      return data.provider_name;
    }
    
    const baseName = filename.replace('.json', '');
    const carrierMappings: Record<string, string> = {
      'ADDP': 'ADDP',
      'BCBSMA': 'Blue Cross Blue Shield Massachusetts',
      'Delta Dental': 'Delta Dental',
      'Guardian': 'Guardian',
      'Humana': 'Humana',
      'Medicare Quick Reference Guide': 'Medicare'
    };
    
    return carrierMappings[baseName] || baseName;
  }

  private extractDocumentationRequirements(data: any, carrierName: string): DocumentationRequirement[] {
    const requirements: DocumentationRequirement[] = [];
    let content = '';
    
    // Extract text content
    if (data.text) {
      content = data.text;
    } else if (data.documents) {
      for (const doc of data.documents) {
        if (doc.pages) {
          for (const page of doc.pages) {
            content += page.content || '';
          }
        }
      }
    }
    
    if (!content) return requirements;
    
    // Extract different types of documentation requirements
    requirements.push(...this.extractRadiographRequirements(content, carrierName));
    requirements.push(...this.extractChartingRequirements(content, carrierName));
    requirements.push(...this.extractMedicalHistoryRequirements(content, carrierName));
    requirements.push(...this.extractPreAuthRequirements(content, carrierName));
    requirements.push(...this.extractNarrativeRequirements(content, carrierName));
    requirements.push(...this.extractPhotoRequirements(content, carrierName));
    requirements.push(...this.extractModelRequirements(content, carrierName));
    
    return requirements;
  }

  private extractRadiographRequirements(content: string, carrierName: string): DocumentationRequirement[] {
    const requirements: DocumentationRequirement[] = [];
    
    // PATTERN EXPLANATION: These regex patterns look for specific phrases about X-ray requirements
    const radiographPatterns = [
      {
        // Looks for "bitewing x-rays required" or similar
        pattern: /bitewing.*?(?:x-ray|radiograph).*?required/gi,
        type: 'Bitewing X-rays',
        description: 'Bitewing radiographs required for diagnosis and treatment planning',
        procedures: ['D0274', 'D1110', 'D2140', 'D2150'] // cleanings, fillings
      },
      {
        // Looks for "panoramic x-ray required" or similar  
        pattern: /panoramic.*?(?:x-ray|radiograph).*?required/gi,
        type: 'Panoramic X-ray',
        description: 'Panoramic radiograph required for comprehensive evaluation',
        procedures: ['D0330', 'D7140', 'D7210'] // extractions, oral surgery
      },
      {
        // Looks for "periapical x-ray required" or similar
        pattern: /periapical.*?(?:x-ray|radiograph).*?required/gi,
        type: 'Periapical X-ray',
        description: 'Periapical radiograph required for endodontic procedures',
        procedures: ['D0220', 'D3310', 'D3320'] // root canals
      },
      {
        // Looks for "full mouth series required" or similar
        pattern: /full.*?mouth.*?(?:series|x-ray|radiograph).*?required/gi,
        type: 'Full Mouth X-ray Series',
        description: 'Complete radiographic survey required',
        procedures: ['D0210', 'D4341', 'D4910'] // comprehensive exams, perio
      }
    ];
    
    for (const { pattern, type, description, procedures } of radiographPatterns) {
      // Test if the pattern matches anywhere in the content
      if (pattern.test(content)) {
        requirements.push({
          document_type: type,
          description: description,
          required_for: procedures
        });
      }
    }
    
    return requirements;
  }

  private extractChartingRequirements(content: string, carrierName: string): DocumentationRequirement[] {
    const requirements: DocumentationRequirement[] = [];
    
    const chartingPatterns = [
      {
        pattern: /periodontal.*?chart.*?required/gi,
        type: 'Periodontal Charting',
        description: 'Periodontal charting with pocket depths and mobility required',
        procedures: ['D4341', 'D4342', 'D4910']
      },
      {
        pattern: /pocket.*?depth.*?chart.*?required/gi,
        type: 'Pocket Depth Chart',
        description: 'Detailed pocket depth measurements required',
        procedures: ['D4341', 'D4342']
      },
      {
        pattern: /mobility.*?chart.*?required/gi,
        type: 'Tooth Mobility Chart',
        description: 'Tooth mobility assessment and documentation required',
        procedures: ['D4341', 'D4910']
      }
    ];
    
    for (const { pattern, type, description, procedures } of chartingPatterns) {
      if (pattern.test(content)) {
        requirements.push({
          document_type: type,
          description: description,
          required_for: procedures
        });
      }
    }
    
    return requirements;
  }

  private extractMedicalHistoryRequirements(content: string, carrierName: string): DocumentationRequirement[] {
    const requirements: DocumentationRequirement[] = [];
    
    const medicalHistoryPatterns = [
      {
        pattern: /medical.*?history.*?required/gi,
        type: 'Medical History',
        description: 'Complete medical history documentation required',
        procedures: ['D0150', 'D4341', 'D7140']
      },
      {
        pattern: /health.*?questionnaire.*?required/gi,
        type: 'Health Questionnaire',
        description: 'Patient health questionnaire must be completed',
        procedures: ['D0150']
      },
      {
        pattern: /medication.*?list.*?required/gi,
        type: 'Medication List',
        description: 'Current medication list documentation required',
        procedures: ['D0150', 'D4341', 'D7140']
      }
    ];
    
    for (const { pattern, type, description, procedures } of medicalHistoryPatterns) {
      if (pattern.test(content)) {
        requirements.push({
          document_type: type,
          description: description,
          required_for: procedures
        });
      }
    }
    
    return requirements;
  }

  private extractPreAuthRequirements(content: string, carrierName: string): DocumentationRequirement[] {
    const requirements: DocumentationRequirement[] = [];
    
    const preAuthPatterns = [
      {
        pattern: /pre-?authorization.*?required/gi,
        type: 'Pre-authorization',
        description: 'Pre-authorization required before treatment',
        procedures: ['D2740', 'D2750', 'D6240', 'D7140']
      },
      {
        pattern: /predetermination.*?required/gi,
        type: 'Predetermination',
        description: 'Predetermination of benefits required',
        procedures: ['D2740', 'D2750', 'D6240']
      }
    ];
    
    for (const { pattern, type, description, procedures } of preAuthPatterns) {
      if (pattern.test(content)) {
        requirements.push({
          document_type: type,
          description: description,
          required_for: procedures
        });
      }
    }
    
    return requirements;
  }

  private extractNarrativeRequirements(content: string, carrierName: string): DocumentationRequirement[] {
    const requirements: DocumentationRequirement[] = [];
    
    if (/narrative.*?required/gi.test(content)) {
      requirements.push({
        document_type: 'Clinical Narrative',
        description: 'Detailed clinical narrative explaining treatment necessity',
        required_for: ['D2740', 'D2750', 'D4341', 'D6240']
      });
    }
    
    return requirements;
  }

  private extractPhotoRequirements(content: string, carrierName: string): DocumentationRequirement[] {
    const requirements: DocumentationRequirement[] = [];
    
    if (/(?:photo|image).*?required/gi.test(content)) {
      requirements.push({
        document_type: 'Clinical Photos',
        description: 'Clinical photographs required for documentation',
        required_for: ['D2740', 'D2750', 'D6240']
      });
    }
    
    return requirements;
  }

  private extractModelRequirements(content: string, carrierName: string): DocumentationRequirement[] {
    const requirements: DocumentationRequirement[] = [];
    
    if (/(?:model|impression).*?required/gi.test(content)) {
      requirements.push({
        document_type: 'Study Models',
        description: 'Study models or impressions required',
        required_for: ['D2740', 'D2750', 'D6240']
      });
    }
    
    return requirements;
  }

  private async storeRequirements(requirements: DocumentationRequirement[]) {
    console.log('💾 Storing documentation requirements in database...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      // Get carrier mappings
      const carrierMap = new Map<string, number>();
      const carriersResult = await client.query('SELECT id, carrier_name FROM insurance_carriers');
      for (const row of carriersResult.rows) {
        carrierMap.set(row.carrier_name.toLowerCase(), row.id);
      }
      
      let insertedCount = 0;
      let skippedCount = 0;
      
      for (const req of requirements) {
        try {
          // Check if requirement already exists
          const existingResult = await client.query(`
            SELECT id FROM documentation_requirements 
            WHERE document_type = $1 AND description = $2
          `, [req.document_type, req.description]);
          
          if (existingResult.rows.length === 0) {
            // Insert new requirement
            await client.query(`
              INSERT INTO documentation_requirements (
                carrier_id, document_type, description, required_for, created_at, updated_at
              )
              VALUES ($1, $2, $3, $4, NOW(), NOW())
            `, [
              req.carrier_id,
              req.document_type,
              req.description,
              JSON.stringify(req.required_for)
            ]);
            
            insertedCount++;
          } else {
            skippedCount++;
          }
          
        } catch (error) {
          console.log(`    ❌ Error inserting requirement:`, error);
          skippedCount++;
        }
      }
      
      console.log(`  ✅ Inserted ${insertedCount} requirements, skipped ${skippedCount} duplicates`);
      
    } finally {
      await client.end();
    }
  }
}

// Run the script
if (require.main === module) {
  const extractor = new DocumentationRequirementsExtractor();
  extractor.run().catch(console.error);
}

export default DocumentationRequirementsExtractor;
