#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface CarrierProcedureRequirement {
  carrier_id: number;
  procedure_id: number;
  documentation_required?: string;
  frequency_limitation?: string;
  age_restrictions?: string;
  other_limitations?: string;
}

interface ExtractedRequirement {
  carrier_name: string;
  procedure_code?: string;
  procedure_category?: string;
  documentation_required?: string;
  frequency_limitation?: string;
  age_restrictions?: string;
  other_limitations?: string;
}

class CarrierProcedureRequirementsExtractor {
  private createClient(): Client {
    return new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING,
      ssl: { rejectUnauthorized: false },
    });
  }

  async run() {
    console.log('📋 Extracting carrier-procedure requirements from documents...');
    
    try {
      await this.extractFromDocuments();
      
      console.log('✅ Carrier-procedure requirements extraction completed!');
      
    } catch (error) {
      console.error('❌ Error extracting requirements:', error);
      process.exit(1);
    }
  }

  private async extractFromDocuments() {
    console.log('📄 Processing parsed documents for requirements...');
    
    const parsedDir = path.join(process.cwd(), '@Parsed');
    
    if (!fs.existsSync(parsedDir)) {
      console.log('  ⚠️  @Parsed directory not found');
      return;
    }
    
    const files = fs.readdirSync(parsedDir);
    const extractedRequirements: ExtractedRequirement[] = [];
    
    for (const file of files) {
      if (!file.endsWith('.json')) continue;
      
      try {
        console.log(`  📄 Processing ${file}...`);
        
        const filePath = path.join(parsedDir, file);
        const content = fs.readFileSync(filePath, 'utf-8');
        const data = JSON.parse(content);
        
        const carrierName = this.extractCarrierName(file, data);
        const requirements = this.extractRequirementsFromDocument(data, carrierName);
        
        extractedRequirements.push(...requirements);
        
        console.log(`    ✅ Extracted ${requirements.length} requirements from ${file}`);
        
      } catch (error) {
        console.log(`    ❌ Error processing ${file}:`, error);
      }
    }
    
    console.log(`  📊 Total extracted requirements: ${extractedRequirements.length}`);
    
    // Store requirements in database
    await this.storeRequirements(extractedRequirements);
  }

  private extractCarrierName(filename: string, data: any): string {
    // Try to extract from provider_name field
    if (data.provider_name) {
      return data.provider_name;
    }
    
    // Extract from filename
    const baseName = filename.replace('.json', '');
    
    // Map common abbreviations to full names
    const carrierMappings: Record<string, string> = {
      'ADDP': 'ADDP',
      'BCBSMA': 'Blue Cross Blue Shield Massachusetts',
      'Availity': 'Availity',
      'Delta Dental': 'Delta Dental',
      'UHC': 'United Healthcare',
      'Medicare Quick Reference Guide': 'Medicare',
      'Aetna': 'Aetna'
    };
    
    return carrierMappings[baseName] || baseName;
  }

  private extractRequirementsFromDocument(data: any, carrierName: string): ExtractedRequirement[] {
    const requirements: ExtractedRequirement[] = [];
    let content = '';
    
    // Extract text content
    if (data.text) {
      content = data.text;
    } else if (data.documents) {
      for (const doc of data.documents) {
        if (doc.pages) {
          for (const page of doc.pages) {
            content += page.content || '';
          }
        }
      }
    }
    
    if (!content) return requirements;
    
    // Extract different types of requirements
    requirements.push(...this.extractFrequencyLimitations(content, carrierName));
    requirements.push(...this.extractDocumentationRequirements(content, carrierName));
    requirements.push(...this.extractAgeRestrictions(content, carrierName));
    requirements.push(...this.extractOtherLimitations(content, carrierName));
    
    return requirements;
  }

  private extractFrequencyLimitations(content: string, carrierName: string): ExtractedRequirement[] {
    const requirements: ExtractedRequirement[] = [];
    const lowerContent = content.toLowerCase();
    
    // Common frequency patterns
    const frequencyPatterns = [
      // Cleanings
      { pattern: /cleaning.*?(?:every\s+)?(\d+)\s+months?/gi, procedure: 'D1110', category: 'Preventive' },
      { pattern: /prophylaxis.*?(?:every\s+)?(\d+)\s+months?/gi, procedure: 'D1110', category: 'Preventive' },
      { pattern: /routine.*?cleaning.*?(?:every\s+)?(\d+)\s+months?/gi, procedure: 'D1110', category: 'Preventive' },
      
      // X-rays
      { pattern: /bitewing.*?(?:every\s+)?(\d+)\s+months?/gi, procedure: 'D0274', category: 'Diagnostic' },
      { pattern: /panoramic.*?(?:every\s+)?(\d+)\s+years?/gi, procedure: 'D0330', category: 'Diagnostic' },
      { pattern: /full.*?mouth.*?x-ray.*?(?:every\s+)?(\d+)\s+years?/gi, procedure: 'D0210', category: 'Diagnostic' },
      
      // Fluoride
      { pattern: /fluoride.*?(?:every\s+)?(\d+)\s+months?/gi, procedure: 'D1206', category: 'Preventive' },
      
      // Crowns
      { pattern: /crown.*?(?:every\s+)?(\d+)\s+years?/gi, procedure: 'D2740', category: 'Restorative' },
      
      // Fillings
      { pattern: /filling.*?(?:every\s+)?(\d+)\s+years?/gi, procedure: 'D2150', category: 'Restorative' },
      
      // Periodontal
      { pattern: /scaling.*?root.*?planing.*?(?:every\s+)?(\d+)\s+months?/gi, procedure: 'D4341', category: 'Periodontal' },
      { pattern: /periodontal.*?maintenance.*?(?:every\s+)?(\d+)\s+months?/gi, procedure: 'D4910', category: 'Periodontal' }
    ];
    
    for (const { pattern, procedure, category } of frequencyPatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        const frequency = match[1];
        const unit = match[0].includes('year') ? 'years' : 'months';
        
        requirements.push({
          carrier_name: carrierName,
          procedure_code: procedure,
          procedure_category: category,
          frequency_limitation: `Every ${frequency} ${unit}`
        });
      }
    }
    
    return requirements;
  }

  private extractDocumentationRequirements(content: string, carrierName: string): ExtractedRequirement[] {
    const requirements: ExtractedRequirement[] = [];
    
    // Documentation patterns
    const docPatterns = [
      { pattern: /x-ray.*?required/gi, documentation: 'X-rays required' },
      { pattern: /radiograph.*?required/gi, documentation: 'Radiographs required' },
      { pattern: /periodontal.*?charting.*?required/gi, documentation: 'Periodontal charting required' },
      { pattern: /medical.*?history.*?required/gi, documentation: 'Medical history required' },
      { pattern: /pre-authorization.*?required/gi, documentation: 'Pre-authorization required' },
      { pattern: /predetermination.*?required/gi, documentation: 'Predetermination required' },
      { pattern: /narrative.*?required/gi, documentation: 'Narrative required' },
      { pattern: /photos.*?required/gi, documentation: 'Photos required' },
      { pattern: /models.*?required/gi, documentation: 'Models required' }
    ];
    
    for (const { pattern, documentation } of docPatterns) {
      if (pattern.test(content)) {
        requirements.push({
          carrier_name: carrierName,
          documentation_required: documentation
        });
      }
    }
    
    return requirements;
  }

  private extractAgeRestrictions(content: string, carrierName: string): ExtractedRequirement[] {
    const requirements: ExtractedRequirement[] = [];
    
    // Age restriction patterns
    const agePatterns = [
      { pattern: /under\s+(\d+).*?not.*?covered/gi, type: 'minimum_age' },
      { pattern: /over\s+(\d+).*?not.*?covered/gi, type: 'maximum_age' },
      { pattern: /age\s+(\d+).*?and.*?under/gi, type: 'age_range' },
      { pattern: /children.*?under\s+(\d+)/gi, type: 'pediatric' },
      { pattern: /adult.*?over\s+(\d+)/gi, type: 'adult' }
    ];
    
    for (const { pattern, type } of agePatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        const age = match[1];
        let restriction = '';
        
        switch (type) {
          case 'minimum_age':
            restriction = `Not covered under age ${age}`;
            break;
          case 'maximum_age':
            restriction = `Not covered over age ${age}`;
            break;
          case 'pediatric':
            restriction = `Pediatric coverage under age ${age}`;
            break;
          case 'adult':
            restriction = `Adult coverage over age ${age}`;
            break;
          default:
            restriction = `Age restriction: ${match[0]}`;
        }
        
        requirements.push({
          carrier_name: carrierName,
          age_restrictions: restriction
        });
      }
    }
    
    return requirements;
  }

  private extractOtherLimitations(content: string, carrierName: string): ExtractedRequirement[] {
    const requirements: ExtractedRequirement[] = [];
    
    // Other limitation patterns
    const limitationPatterns = [
      { pattern: /missing.*?tooth.*?clause/gi, limitation: 'Missing tooth clause applies' },
      { pattern: /waiting.*?period.*?(\d+).*?months?/gi, limitation: 'Waiting period applies' },
      { pattern: /annual.*?maximum.*?\$?(\d+)/gi, limitation: 'Annual maximum applies' },
      { pattern: /lifetime.*?maximum.*?\$?(\d+)/gi, limitation: 'Lifetime maximum applies' },
      { pattern: /not.*?covered.*?cosmetic/gi, limitation: 'Cosmetic procedures not covered' },
      { pattern: /experimental.*?not.*?covered/gi, limitation: 'Experimental procedures not covered' },
      { pattern: /replacement.*?limitation/gi, limitation: 'Replacement limitations apply' }
    ];
    
    for (const { pattern, limitation } of limitationPatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        let finalLimitation = limitation;
        
        if (match[1]) {
          finalLimitation = finalLimitation.replace(/applies?/, `${match[1]} applies`);
        }
        
        requirements.push({
          carrier_name: carrierName,
          other_limitations: finalLimitation
        });
      }
    }
    
    return requirements;
  }

  private async storeRequirements(extractedRequirements: ExtractedRequirement[]) {
    console.log('💾 Storing requirements in database...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      // Get carrier and procedure mappings
      const carrierMap = new Map<string, number>();
      const procedureMap = new Map<string, number>();
      
      const carriersResult = await client.query('SELECT id, carrier_name FROM insurance_carriers');
      for (const row of carriersResult.rows) {
        carrierMap.set(row.carrier_name.toLowerCase(), row.id);
      }
      
      const proceduresResult = await client.query('SELECT id, procedure_code FROM procedures');
      for (const row of proceduresResult.rows) {
        procedureMap.set(row.procedure_code, row.id);
      }
      
      let insertedCount = 0;
      let skippedCount = 0;
      
      for (const req of extractedRequirements) {
        try {
          // Find carrier ID
          const carrierId = this.findCarrierId(req.carrier_name, carrierMap);
          
          if (!carrierId) {
            console.log(`    ⚠️  Carrier not found: ${req.carrier_name}`);
            skippedCount++;
            continue;
          }
          
          // Find procedure ID (if specified)
          let procedureId = null;
          if (req.procedure_code) {
            procedureId = procedureMap.get(req.procedure_code);
            if (!procedureId) {
              console.log(`    ⚠️  Procedure not found: ${req.procedure_code}`);
            }
          }
          
          // Insert requirement
          await client.query(`
            INSERT INTO carrier_procedure_requirements (
              carrier_id, procedure_id, documentation_required, 
              frequency_limitation, age_restrictions, other_limitations,
              created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
          `, [
            carrierId,
            procedureId,
            req.documentation_required,
            req.frequency_limitation,
            req.age_restrictions,
            req.other_limitations
          ]);
          
          insertedCount++;
          
        } catch (error) {
          console.log(`    ❌ Error inserting requirement:`, error);
          skippedCount++;
        }
      }
      
      console.log(`  ✅ Inserted ${insertedCount} requirements, skipped ${skippedCount}`);
      
    } finally {
      await client.end();
    }
  }

  private findCarrierId(carrierName: string, carrierMap: Map<string, number>): number | null {
    const lowerName = carrierName.toLowerCase();
    
    // Direct match
    if (carrierMap.has(lowerName)) {
      return carrierMap.get(lowerName)!;
    }
    
    // Fuzzy matching for common variations
    for (const [dbName, id] of carrierMap) {
      if (dbName.includes(lowerName) || lowerName.includes(dbName)) {
        return id;
      }
    }
    
    return null;
  }
}

// Run the script
if (require.main === module) {
  const extractor = new CarrierProcedureRequirementsExtractor();
  extractor.run().catch(console.error);
}

export default CarrierProcedureRequirementsExtractor;
