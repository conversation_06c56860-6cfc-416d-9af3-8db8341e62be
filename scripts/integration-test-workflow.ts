#!/usr/bin/env ts-node

import { DatabaseUtils } from '../src/utils/database';

class IntegrationTestWorkflow {
  private dbUtils: DatabaseUtils;

  constructor() {
    this.dbUtils = DatabaseUtils.getInstance();
  }

  async testCompleteWorkflow(): Promise<void> {
    console.log('🔄 Testing Complete Dental Narrator Workflow');
    console.log('============================================\n');

    // Scenario: A dental office needs to validate coverage for a crown procedure
    console.log('📋 Scenario: Crown Procedure Coverage Validation');
    console.log('Patient: <PERSON>, Age 45');
    console.log('Insurance: Delta Dental');
    console.log('Procedure: D2740 (Crown - porcelain/ceramic substrate)\n');

    // Step 1: Look up the carrier
    console.log('Step 1: Looking up insurance carrier...');
    const carrier = await this.dbUtils.lookupCarrier('Delta Dental');
    if (carrier) {
      console.log(`✅ Found carrier: ${carrier.name} (Payer ID: ${carrier.payer_id})`);
    } else {
      console.log('❌ Carrier not found');
      return;
    }

    // Step 2: Look up the procedure
    console.log('\nStep 2: Looking up procedure information...');
    const procedures = await this.dbUtils.lookupProcedure('D2740');
    if (procedures.length > 0) {
      const procedure = procedures[0];
      console.log(`✅ Found procedure: ${procedure.code} - ${procedure.name}`);
      console.log(`   Category: ${procedure.category}`);
    } else {
      console.log('❌ Procedure not found');
      return;
    }

    // Step 3: Search for coverage guidelines
    console.log('\nStep 3: Searching for coverage guidelines...');
    const guidelines = await this.dbUtils.searchGuidelines('Delta Dental crown coverage requirements', {
      carrier: 'Delta',
      limit: 5
    });
    console.log(`✅ Found ${guidelines.length} relevant guidelines`);
    
    if (guidelines.length > 0) {
      console.log('   Top guidelines:');
      guidelines.slice(0, 3).forEach((guideline, index) => {
        console.log(`   ${index + 1}. ${guideline.title}`);
        console.log(`      Similarity: ${guideline.similarity || 'N/A'}`);
      });
    }

    // Step 4: Check documentation requirements
    console.log('\nStep 4: Checking documentation requirements...');
    const docRequirements = await this.dbUtils.searchGuidelines('crown documentation requirements x-ray', {
      limit: 3
    });
    console.log(`✅ Found ${docRequirements.length} documentation requirements`);

    // Step 5: Check frequency limitations
    console.log('\nStep 5: Checking frequency limitations...');
    const client = this.dbUtils.createClient();
    await client.connect();
    
    try {
      const frequencyResult = await client.query(`
        SELECT frequency_limitation 
        FROM carrier_procedure_requirements 
        WHERE frequency_limitation IS NOT NULL 
        AND frequency_limitation ILIKE '%year%'
        LIMIT 3
      `);
      
      if (frequencyResult.rows.length > 0) {
        console.log('✅ Found frequency limitations:');
        frequencyResult.rows.forEach((row, index) => {
          console.log(`   ${index + 1}. ${row.frequency_limitation}`);
        });
      } else {
        console.log('ℹ️  No specific frequency limitations found for this procedure');
      }
    } finally {
      await client.end();
    }

    // Step 6: Generate summary
    console.log('\n📊 Coverage Validation Summary:');
    console.log('================================');
    console.log(`✅ Carrier: ${carrier.name} - FOUND`);
    console.log(`✅ Procedure: D2740 Crown - FOUND`);
    console.log(`✅ Guidelines: ${guidelines.length} relevant documents`);
    console.log(`✅ Documentation: ${docRequirements.length} requirements found`);
    console.log('✅ System Performance: All queries completed successfully');

    console.log('\n🎯 Workflow Result: SUCCESSFUL');
    console.log('The dental office has all the information needed to:');
    console.log('• Verify patient coverage');
    console.log('• Understand documentation requirements');
    console.log('• Follow proper submission procedures');
    console.log('• Check for any frequency limitations');
  }

  async testSearchAccuracy(): Promise<void> {
    console.log('\n🎯 Testing Search Accuracy');
    console.log('==========================\n');

    const testQueries = [
      {
        query: 'cleaning frequency requirements',
        expectedTerms: ['cleaning', 'prophylaxis', 'frequency', 'months']
      },
      {
        query: 'crown documentation x-ray requirements',
        expectedTerms: ['crown', 'x-ray', 'documentation', 'radiograph']
      },
      {
        query: 'Delta Dental preauthorization',
        expectedTerms: ['Delta', 'preauthorization', 'prior authorization']
      }
    ];

    for (const test of testQueries) {
      console.log(`Testing query: "${test.query}"`);
      
      const results = await this.dbUtils.searchGuidelines(test.query, { limit: 5 });
      console.log(`✅ Found ${results.length} results`);
      
      if (results.length > 0) {
        const topResult = results[0];
        console.log(`   Top result: ${topResult.title}`);
        console.log(`   Similarity: ${topResult.similarity || 'N/A'}`);
        
        // Check if expected terms appear in results
        const contentText = JSON.stringify(topResult.content).toLowerCase();
        const foundTerms = test.expectedTerms.filter(term => 
          contentText.includes(term.toLowerCase())
        );
        
        console.log(`   Expected terms found: ${foundTerms.length}/${test.expectedTerms.length}`);
        if (foundTerms.length > 0) {
          console.log(`   Found: ${foundTerms.join(', ')}`);
        }
      }
      console.log('');
    }
  }

  async testSystemLoad(): Promise<void> {
    console.log('⚡ Testing System Load Handling');
    console.log('==============================\n');

    const concurrentQueries = 5;
    const queries = [
      'crown coverage requirements',
      'cleaning frequency limitations',
      'x-ray documentation needed',
      'preauthorization process',
      'appeal procedures'
    ];

    console.log(`Running ${concurrentQueries} concurrent searches...`);
    
    const startTime = Date.now();
    
    const promises = queries.map(async (query, index) => {
      const start = Date.now();
      const results = await this.dbUtils.searchGuidelines(query, { limit: 3 });
      const duration = Date.now() - start;
      
      return {
        query,
        resultCount: results.length,
        duration
      };
    });

    const results = await Promise.all(promises);
    const totalDuration = Date.now() - startTime;

    console.log('✅ Concurrent search results:');
    results.forEach((result, index) => {
      console.log(`   ${index + 1}. "${result.query}": ${result.resultCount} results (${result.duration}ms)`);
    });

    console.log(`\n📊 Load Test Summary:`);
    console.log(`   Total time: ${totalDuration}ms`);
    console.log(`   Average per query: ${Math.round(totalDuration / concurrentQueries)}ms`);
    console.log(`   Queries per second: ${(concurrentQueries / (totalDuration / 1000)).toFixed(2)}`);

    if (totalDuration < 10000) {
      console.log('✅ System handles concurrent load well');
    } else {
      console.log('⚠️  System may need optimization for high load');
    }
  }

  async runAllTests(): Promise<void> {
    try {
      await this.testCompleteWorkflow();
      await this.testSearchAccuracy();
      await this.testSystemLoad();
      
      console.log('\n🎉 INTEGRATION TESTS COMPLETED SUCCESSFULLY!');
      console.log('The Dental Narrator system is fully functional and ready for use.');
      
    } catch (error) {
      console.error('\n❌ Integration test failed:', error);
      throw error;
    }
  }
}

// Main execution
async function main() {
  const integrationTest = new IntegrationTestWorkflow();
  await integrationTest.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { IntegrationTestWorkflow };
