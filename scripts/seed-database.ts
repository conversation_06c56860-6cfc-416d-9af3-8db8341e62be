#!/usr/bin/env ts-node

import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';
import { openai } from '@ai-sdk/openai';
import { embedMany } from 'ai';
import { Pool } from 'pg';

// Load environment variables
config({ path: '.env.development' });

// Database connection
const dbUrl = process.env.POSTGRES_CONNECTION_STRING || 'postgresql://postgres:postgres@127.0.0.1:54322/postgres';
const pool = new Pool({ connectionString: dbUrl });

interface ParsedDocument {
  provider_name?: string;
  filename?: string;
  total_pages?: number;
  documents?: Array<{
    filename: string;
    total_pages: number;
    pages: Array<{
      page_number: number;
      content: string;
    }>;
  }>;
  pages?: Array<{
    page_number: number;
    content: string;
  }>;
  // For glossary data
  type?: string;
  term?: string;
  definition?: string;
  letter?: string;
}

interface InsuranceCarrier {
  name: string;
  code: string;
  website?: string;
  contact_info?: any;
}

interface Guideline {
  carrier_id: number;
  category: string;
  title: string;
  content: any;
  metadata?: any;
}

interface GlossaryTerm {
  term: string;
  definition: string;
  category?: string;
  letter?: string;
}

class DatabaseSeeder {
  private carriers: Map<string, number> = new Map();

  async seedDatabase() {
    console.log('🌱 Starting database seeding...');
    
    try {
      // Step 1: Clear existing data
      await this.clearExistingData();
      
      // Step 2: Seed insurance carriers
      await this.seedInsuranceCarriers();
      
      // Step 3: Seed glossary terms
      await this.seedGlossaryTerms();
      
      // Step 4: Seed carrier-specific guidelines
      await this.seedCarrierGuidelines();
      
      // Step 5: Seed appeal procedures
      await this.seedAppealProcedures();
      
      // Step 6: Generate and store embeddings
      await this.generateEmbeddings();
      
      console.log('✅ Database seeding completed successfully!');
      
    } catch (error) {
      console.error('❌ Error seeding database:', error);
      throw error;
    }
  }

  private async clearExistingData() {
    console.log('🧹 Clearing existing data...');
    
    const tables = [
      'embeddings',
      'search_logs', 
      'appeal_procedures',
      'documentation_requirements',
      'carrier_procedure_requirements',
      'guidelines',
      'glossary_terms',
      'procedures',
      'insurance_carriers'
    ];
    
    for (const table of tables) {
      await pool.query(`DELETE FROM ${table}`);
    }
    
    console.log('✅ Existing data cleared');
  }

  private async seedInsuranceCarriers() {
    console.log('📊 Seeding insurance carriers...');
    
    const parsedDir = path.join(process.cwd(), '@Parsed');
    const files = fs.readdirSync(parsedDir);
    
    // Extract carrier names from filenames
    const carrierNames = new Set<string>();
    
    for (const file of files) {
      if (file.endsWith('.json') && !file.includes('glossary') && !file.includes('appeal') && 
          !file.includes('claim-documentation') && !file.includes('supporting') && 
          !file.includes('complaints') && !file.includes('independent') && 
          !file.includes('notice') && !file.includes('ppo-office')) {
        
        let carrierName = file.replace('.json', '').replace('_data', '');
        
        // Clean up carrier names
        const cleanName = this.cleanCarrierName(carrierName);
        if (cleanName) {
          carrierNames.add(cleanName);
        }
      }
    }
    
    // Insert carriers into database
    for (const carrierName of carrierNames) {
      const code = this.generateCarrierCode(carrierName);
      
      const result = await pool.query(
        'INSERT INTO insurance_carriers (name, code, created_at, updated_at) VALUES ($1, $2, NOW(), NOW()) RETURNING id',
        [carrierName, code]
      );
      
      this.carriers.set(carrierName.toLowerCase(), result.rows[0].id);
      console.log(`   ✓ Added carrier: ${carrierName} (${code})`);
    }
    
    console.log(`✅ Seeded ${carrierNames.size} insurance carriers`);
  }

  private cleanCarrierName(name: string): string {
    // Clean up carrier names
    const cleanMappings: Record<string, string> = {
      'Delta Dental': 'Delta Dental',
      'BCBSMA': 'Blue Cross Blue Shield Massachusetts',
      'UHC': 'United Healthcare',
      'UMR': 'UMR',
      'bluecross premera': 'Premera Blue Cross',
      'ameritas_data': 'Ameritas',
      'Tricarewest': 'Tricare West',
      'Misc-General': 'General Insurance',
      'Medicare Quick Reference Guide': 'Medicare'
    };
    
    return cleanMappings[name] || name.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  private generateCarrierCode(name: string): string {
    // Generate short codes for carriers
    const codeMappings: Record<string, string> = {
      'Delta Dental': 'DELTA',
      'Blue Cross Blue Shield Massachusetts': 'BCBSMA',
      'United Healthcare': 'UHC',
      'Premera Blue Cross': 'PREMERA',
      'Ameritas': 'AMERITAS',
      'Guardian': 'GUARDIAN',
      'Humana': 'HUMANA',
      'MetLife': 'METLIFE',
      'Cigna': 'CIGNA',
      'Lincoln': 'LINCOLN',
      'Principal': 'PRINCIPAL'
    };
    
    return codeMappings[name] || name.replace(/\s+/g, '').substring(0, 8).toUpperCase();
  }

  private async seedGlossaryTerms() {
    console.log('📚 Seeding glossary terms...');
    
    const glossaryPath = path.join(process.cwd(), '@Parsed', 'glossary_data.json');
    
    if (!fs.existsSync(glossaryPath)) {
      console.log('   ⚠️  Glossary file not found, skipping...');
      return;
    }
    
    const glossaryData: GlossaryTerm[] = JSON.parse(fs.readFileSync(glossaryPath, 'utf8'));
    
    let termCount = 0;
    for (const item of glossaryData) {
      if (item.term && item.definition) {
        await pool.query(
          'INSERT INTO glossary_terms (term, definition, letter, category, created_at, updated_at) VALUES ($1, $2, $3, $4, NOW(), NOW())',
          [item.term, item.definition, item.letter || item.term.charAt(0).toUpperCase(), 'dental']
        );
        termCount++;
      }
    }
    
    console.log(`✅ Seeded ${termCount} glossary terms`);
  }

  private async seedCarrierGuidelines() {
    console.log('📋 Seeding carrier guidelines...');
    
    const parsedDir = path.join(process.cwd(), '@Parsed');
    const files = fs.readdirSync(parsedDir);
    
    for (const file of files) {
      if (file.endsWith('.json') && !file.includes('glossary') && !file.includes('appeal') && 
          !file.includes('claim-documentation') && !file.includes('supporting') && 
          !file.includes('complaints') && !file.includes('independent') && 
          !file.includes('notice') && !file.includes('ppo-office')) {
        
        await this.processCarrierFile(path.join(parsedDir, file));
      }
    }
    
    console.log('✅ Carrier guidelines seeded');
  }

  private async processCarrierFile(filePath: string) {
    const fileName = path.basename(filePath, '.json');
    const carrierName = this.cleanCarrierName(fileName.replace('_data', ''));
    const carrierId = this.carriers.get(carrierName.toLowerCase());
    
    if (!carrierId) {
      console.log(`   ⚠️  Carrier ID not found for: ${carrierName}`);
      return;
    }
    
    try {
      const data: ParsedDocument = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      
      // Handle different data structures
      if (data.documents) {
        // Standard document structure
        for (const doc of data.documents) {
          await this.processDocument(carrierId, carrierName, doc.filename, doc.pages);
        }
      } else if (data.pages) {
        // Direct pages structure
        await this.processDocument(carrierId, carrierName, data.filename || fileName, data.pages);
      } else if (Array.isArray(data)) {
        // Array of items (like complaints.json)
        for (const item of data) {
          if (item.content || item.definition) {
            await this.createGuideline(carrierId, 'general', item.title || 'General Guideline', {
              content: item.content || item.definition,
              source: fileName
            });
          }
        }
      } else {
        console.log(`   ⚠️  Unknown data structure in: ${fileName}`);
      }
      
      console.log(`   ✓ Processed: ${carrierName}`);
      
    } catch (error) {
      console.error(`   ❌ Error processing ${fileName}:`, error);
    }
  }

  private async processDocument(carrierId: number, carrierName: string, filename: string, pages: Array<{page_number: number, content: string}>) {
    // Combine all pages into logical sections
    const allContent = pages.map(p => p.content).join('\n\n');
    
    // Determine category based on filename and content
    const category = this.determineCategory(filename, allContent);
    
    // Split content into chunks for better organization
    const chunks = this.chunkContent(allContent, 2000);
    
    for (let i = 0; i < chunks.length; i++) {
      await this.createGuideline(carrierId, category, `${filename} - Part ${i + 1}`, {
        content: chunks[i],
        source_file: filename,
        page_count: pages.length,
        chunk: i + 1,
        total_chunks: chunks.length
      });
    }
  }

  private determineCategory(filename: string, content: string): string {
    const lowerFilename = filename.toLowerCase();
    const lowerContent = content.toLowerCase();
    
    if (lowerFilename.includes('cdt') || lowerContent.includes('cdt')) return 'procedures';
    if (lowerFilename.includes('manual') || lowerFilename.includes('handbook')) return 'general';
    if (lowerFilename.includes('appeal')) return 'appeals';
    if (lowerFilename.includes('claim')) return 'claims';
    if (lowerFilename.includes('access') || lowerFilename.includes('appointment')) return 'access';
    if (lowerContent.includes('orthodontic')) return 'orthodontic';
    if (lowerContent.includes('periodontal')) return 'periodontal';
    if (lowerContent.includes('crown') || lowerContent.includes('bridge')) return 'restorative';
    if (lowerContent.includes('root canal') || lowerContent.includes('endodontic')) return 'endodontic';
    if (lowerContent.includes('implant')) return 'implant';
    
    return 'general';
  }

  private chunkContent(content: string, maxLength: number): string[] {
    if (content.length <= maxLength) return [content];
    
    const chunks: string[] = [];
    const sentences = content.split(/[.!?]+/).filter(s => s.trim());
    
    let currentChunk = '';
    for (const sentence of sentences) {
      if (currentChunk.length + sentence.length > maxLength && currentChunk) {
        chunks.push(currentChunk.trim());
        currentChunk = sentence;
      } else {
        currentChunk += (currentChunk ? '. ' : '') + sentence;
      }
    }
    
    if (currentChunk) chunks.push(currentChunk.trim());
    
    return chunks;
  }

  private async createGuideline(carrierId: number, category: string, title: string, content: any) {
    await pool.query(
      'INSERT INTO guidelines (carrier_id, category, title, content, metadata, created_at, updated_at) VALUES ($1, $2, $3, $4, $5, NOW(), NOW())',
      [carrierId, category, title, JSON.stringify(content), JSON.stringify({ source: 'parsed_documents' })]
    );
  }

  private async seedAppealProcedures() {
    console.log('⚖️  Seeding appeal procedures...');
    
    const appealPath = path.join(process.cwd(), '@Parsed', 'appeal procedures.json');
    
    if (fs.existsSync(appealPath)) {
      const appealData = JSON.parse(fs.readFileSync(appealPath, 'utf8'));
      
      // Process appeal procedures for each carrier
      for (const [carrierName, carrierId] of this.carriers) {
        await pool.query(
          'INSERT INTO appeal_procedures (carrier_id, procedure_type, steps, timeframes, created_at, updated_at) VALUES ($1, $2, $3, $4, NOW(), NOW())',
          [
            carrierId,
            'general_appeal',
            JSON.stringify(['Submit written appeal within 180 days', 'Include supporting documentation', 'Allow 30 days for review']),
            JSON.stringify({ initial_review: '30 days', 'external_review': '60 days' })
          ]
        );
      }
      
      console.log('✅ Appeal procedures seeded');
    }
  }

  private async generateEmbeddings() {
    console.log('🔢 Generating embeddings for guidelines...');
    
    // Fetch all guidelines
    const result = await pool.query('SELECT id, content FROM guidelines');
    const guidelines = result.rows;
    
    console.log(`   Processing ${guidelines.length} guidelines...`);
    
    // Process in batches to avoid API limits
    const batchSize = 10;
    for (let i = 0; i < guidelines.length; i += batchSize) {
      const batch = guidelines.slice(i, i + batchSize);
      
      try {
        const texts = batch.map(g => {
          try {
            const content = typeof g.content === 'string' ? JSON.parse(g.content) : g.content;
            return typeof content === 'string' ? content : content.content || JSON.stringify(content);
          } catch (e) {
            return g.content.toString(); // Fallback to string representation
          }
        });
        
        const { embeddings } = await embedMany({
          model: openai.embedding('text-embedding-3-small'),
          values: texts
        });
        
        // Store embeddings
        for (let j = 0; j < batch.length; j++) {
          const guideline = batch[j];
          const embedding = embeddings[j];
          
          await pool.query(
            'INSERT INTO embeddings (content_type, content_id, embedding, metadata, created_at, updated_at) VALUES ($1, $2, $3, $4, NOW(), NOW())',
            [
              'guideline',
              guideline.id,
              `[${embedding.join(',')}]`,
              JSON.stringify({ source: 'guidelines' })
            ]
          );
        }
        
        console.log(`   ✓ Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(guidelines.length/batchSize)}`);
        
        // Small delay to respect API limits
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.error(`   ❌ Error processing batch ${Math.floor(i/batchSize) + 1}:`, error);
      }
    }
    
    console.log('✅ Embeddings generated');
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting Dental Narrator database seeding...');
  
  // Check required environment variables
  if (!process.env.OPENAI_API_KEY) {
    console.error('❌ OPENAI_API_KEY environment variable is required');
    process.exit(1);
  }
  
  const seeder = new DatabaseSeeder();
  
  try {
    await seeder.seedDatabase();
    console.log('🎉 Database seeding completed successfully!');
  } catch (error) {
    console.error('💥 Database seeding failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}