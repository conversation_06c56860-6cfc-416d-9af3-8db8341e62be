#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

class FrequencyLimitationChecker {
  private createClient(): Client {
    return new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING || process.env.DATABASE_URL
    });
  }

  async run() {
    console.log('🔍 Checking frequency limitations in database...');
    
    const client = this.createClient();
    await client.connect();

    try {
      // Check total count of frequency limitations
      const countResult = await client.query(`
        SELECT COUNT(*) as total_count 
        FROM carrier_procedure_requirements 
        WHERE frequency_limitation IS NOT NULL
      `);
      
      console.log(`📊 Total frequency limitations: ${countResult.rows[0].total_count}`);

      // Get distinct frequency limitations
      const distinctResult = await client.query(`
        SELECT DISTINCT frequency_limitation, COUNT(*) as count
        FROM carrier_procedure_requirements 
        WHERE frequency_limitation IS NOT NULL
        GROUP BY frequency_limitation
        ORDER BY count DESC
      `);

      console.log('\n📋 Frequency limitations found:');
      for (const row of distinctResult.rows) {
        console.log(`  • ${row.frequency_limitation} (${row.count} occurrences)`);
      }

      // Get some examples with carrier and procedure info
      const examplesResult = await client.query(`
        SELECT
          cpr.carrier_id,
          cpr.procedure_id,
          cpr.frequency_limitation
        FROM carrier_procedure_requirements cpr
        WHERE cpr.frequency_limitation IS NOT NULL
        LIMIT 10
      `);

      console.log('\n🔍 Examples of frequency limitations:');
      for (const row of examplesResult.rows) {
        console.log(`  • Carrier ID ${row.carrier_id}, Procedure ID ${row.procedure_id}: ${row.frequency_limitation}`);
      }

    } finally {
      await client.end();
    }
  }
}

// Run the checker
if (require.main === module) {
  const checker = new FrequencyLimitationChecker();
  checker.run().catch(console.error);
}
