#!/usr/bin/env ts-node

import { DatabaseUtils } from '../src/utils/database';

class DatabaseSchemaChecker {
  private dbUtils: DatabaseUtils;

  constructor() {
    this.dbUtils = DatabaseUtils.getInstance();
  }

  async checkTableSchema(tableName: string): Promise<void> {
    console.log(`\n📋 Checking schema for table: ${tableName}`);
    console.log('='.repeat(50));
    
    try {
      const client = this.dbUtils.createClient();
      await client.connect();

      try {
        // Get column information
        const result = await client.query(`
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default
          FROM information_schema.columns 
          WHERE table_name = $1 
          ORDER BY ordinal_position
        `, [tableName]);

        if (result.rows.length === 0) {
          console.log(`❌ Table '${tableName}' not found`);
          return;
        }

        console.log('Columns:');
        result.rows.forEach((column, index) => {
          console.log(`  ${index + 1}. ${column.column_name} (${column.data_type})`);
          console.log(`     Nullable: ${column.is_nullable}`);
          if (column.column_default) {
            console.log(`     Default: ${column.column_default}`);
          }
        });

        // Get sample data
        const sampleResult = await client.query(`SELECT * FROM ${tableName} LIMIT 3`);
        
        if (sampleResult.rows.length > 0) {
          console.log('\nSample data:');
          sampleResult.rows.forEach((row, index) => {
            console.log(`  Row ${index + 1}:`, Object.keys(row).slice(0, 5).map(key => `${key}: ${row[key]}`).join(', '));
          });
        }

      } finally {
        await client.end();
      }

    } catch (error) {
      console.error(`❌ Error checking ${tableName}:`, error);
    }
  }

  async checkAllTables(): Promise<void> {
    console.log('🔍 Database Schema Analysis');
    console.log('===========================');

    const tables = [
      'insurance_carriers',
      'procedures', 
      'guidelines',
      'embeddings',
      'documentation_requirements',
      'carrier_procedure_requirements',
      'appeal_procedures',
      'glossary_terms'
    ];

    for (const table of tables) {
      await this.checkTableSchema(table);
    }
  }

  async checkTableCounts(): Promise<void> {
    console.log('\n📊 Table Row Counts');
    console.log('==================');
    
    try {
      const client = this.dbUtils.createClient();
      await client.connect();

      try {
        const tables = [
          'insurance_carriers',
          'procedures', 
          'guidelines',
          'embeddings',
          'documentation_requirements',
          'carrier_procedure_requirements',
          'appeal_procedures',
          'glossary_terms'
        ];

        for (const table of tables) {
          try {
            const result = await client.query(`SELECT COUNT(*) as count FROM ${table}`);
            console.log(`✅ ${table}: ${result.rows[0].count} rows`);
          } catch (error) {
            console.log(`❌ ${table}: Error - ${error}`);
          }
        }

      } finally {
        await client.end();
      }

    } catch (error) {
      console.error('❌ Error checking table counts:', error);
    }
  }
}

// Main execution
async function main() {
  const checker = new DatabaseSchemaChecker();
  await checker.checkTableCounts();
  await checker.checkAllTables();
}

if (require.main === module) {
  main().catch(console.error);
}

export { DatabaseSchemaChecker };
