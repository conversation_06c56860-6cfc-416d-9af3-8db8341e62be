#!/usr/bin/env ts-node

import { app } from '../src/api/server';
import { Server } from 'http';

class APIEndpointTester {
  private server: Server | null = null;
  private port = 3001;

  async startServer(): Promise<void> {
    return new Promise((resolve) => {
      this.server = app.listen(this.port, () => {
        console.log(`🚀 Test server started on port ${this.port}`);
        resolve();
      });
    });
  }

  async stopServer(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('🛑 Test server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  async testEndpoint(method: string, path: string, body?: any): Promise<any> {
    const url = `http://localhost:${this.port}${path}`;
    
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: body ? JSON.stringify(body) : undefined,
      });

      const data = await response.json();
      
      console.log(`${response.ok ? '✅' : '❌'} ${method} ${path} - Status: ${response.status}`);
      
      if (!response.ok) {
        console.log(`   Error: ${data.error || 'Unknown error'}`);
      } else {
        console.log(`   Results: ${data.results_count || data.results?.length || 'N/A'} items`);
      }

      return { status: response.status, data };

    } catch (error) {
      console.log(`❌ ${method} ${path} - Network Error: ${error}`);
      return { status: 0, error };
    }
  }

  async runTests(): Promise<void> {
    console.log('🧪 Testing Dental Narrator API Endpoints');
    console.log('==========================================\n');

    try {
      await this.startServer();
      
      // Wait a moment for server to be ready
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test 1: Health Check
      console.log('📋 Test 1: Health Check');
      await this.testEndpoint('GET', '/health');
      console.log('');

      // Test 2: Search Guidelines
      console.log('📋 Test 2: Search Guidelines');
      await this.testEndpoint('POST', '/search/guidelines', {
        query: 'crown procedure requirements',
        limit: 3
      });
      console.log('');

      // Test 3: Search Guidelines with Carrier Filter
      console.log('📋 Test 3: Search Guidelines with Carrier Filter');
      await this.testEndpoint('POST', '/search/guidelines', {
        query: 'cleaning frequency',
        carrier: 'Delta',
        limit: 3
      });
      console.log('');

      // Test 4: Search Procedures
      console.log('📋 Test 4: Search Procedures');
      await this.testEndpoint('POST', '/search/procedures', {
        query: 'crown',
        limit: 5
      });
      console.log('');

      // Test 5: Validate Coverage
      console.log('📋 Test 5: Validate Coverage');
      await this.testEndpoint('POST', '/validate/coverage', {
        carrier: 'Delta Dental',
        procedure_codes: ['D2740', 'D1110'],
        patient_info: {
          age: 35
        }
      });
      console.log('');

      // Test 6: Glossary Lookup
      console.log('📋 Test 6: Glossary Lookup');
      await this.testEndpoint('POST', '/glossary/lookup', {
        term: 'preauthorization',
        exact_match: false
      });
      console.log('');

      // Test 7: Get Carriers
      console.log('📋 Test 7: Get Carriers');
      await this.testEndpoint('GET', '/api/carriers?limit=5');
      console.log('');

      // Test 8: Get Procedures
      console.log('📋 Test 8: Get Procedures');
      await this.testEndpoint('GET', '/api/procedures?limit=5&category=restorative');
      console.log('');

      // Test 9: Get Appeals
      console.log('📋 Test 9: Get Appeals');
      await this.testEndpoint('GET', '/api/appeals?limit=5');
      console.log('');

      // Test 10: Error Handling - Invalid Request
      console.log('📋 Test 10: Error Handling');
      await this.testEndpoint('POST', '/search/guidelines', {
        // Missing required 'query' field
        limit: 5
      });
      console.log('');

      // Test 11: 404 Handling
      console.log('📋 Test 11: 404 Handling');
      await this.testEndpoint('GET', '/nonexistent-endpoint');
      console.log('');

      console.log('🎉 API endpoint testing completed!');

    } catch (error) {
      console.error('❌ Test execution failed:', error);
    } finally {
      await this.stopServer();
    }
  }
}

// Performance test
async function performanceTest(): Promise<void> {
  console.log('\n⚡ Performance Test');
  console.log('==================');

  const tester = new APIEndpointTester();
  await tester.startServer();

  try {
    const testCases = [
      { name: 'Guidelines Search', path: '/search/guidelines', body: { query: 'crown requirements', limit: 5 } },
      { name: 'Procedure Search', path: '/search/procedures', body: { query: 'cleaning', limit: 10 } },
      { name: 'Coverage Validation', path: '/validate/coverage', body: { carrier: 'Delta', procedure_codes: ['D1110'] } }
    ];

    for (const testCase of testCases) {
      const startTime = Date.now();
      
      await fetch(`http://localhost:3001${testCase.path}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testCase.body)
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`⏱️  ${testCase.name}: ${duration}ms`);
    }

  } finally {
    await tester.stopServer();
  }
}

// Run tests
async function main() {
  const tester = new APIEndpointTester();
  await tester.runTests();
  await performanceTest();
}

if (require.main === module) {
  main().catch(console.error);
}

export { APIEndpointTester };
