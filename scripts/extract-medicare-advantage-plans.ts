#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface MedicareAdvantagePlan {
  carrier_id: number;
  plan_name: string;
  states_covered: string[];
  contract_number?: string;
  pbp_number?: string;
  maximum_benefit?: string;
  out_of_network_coverage?: boolean;
  effective_date?: string;
  notes?: string;
}

class MedicareAdvantageExtractor {
  private createClient(): Client {
    return new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING,
      ssl: { rejectUnauthorized: false },
    });
  }

  async run() {
    console.log('🏥 Extracting Medicare Advantage plans from documents...');
    
    try {
      await this.extractFromMedicareDocument();
      
      console.log('✅ Medicare Advantage plans extracted successfully!');
      
    } catch (error) {
      console.error('❌ Error extracting Medicare Advantage plans:', error);
      process.exit(1);
    }
  }

  private async extractFromMedicareDocument() {
    console.log('📄 Processing Medicare Quick Reference Guide...');
    
    const fs = require('fs');
    const path = require('path');
    
    try {
      const filePath = path.join(process.cwd(), '@Parsed', 'Medicare Quick Reference Guide.json');
      const content = fs.readFileSync(filePath, 'utf-8');
      const data = JSON.parse(content);
      
      const plans: MedicareAdvantagePlan[] = [];
      
      // Get Aetna carrier ID
      const client = this.createClient();
      await client.connect();
      
      try {
        const carrierResult = await client.query(
          "SELECT id FROM insurance_carriers WHERE carrier_name ILIKE '%aetna%' LIMIT 1"
        );
        
        if (carrierResult.rows.length === 0) {
          console.log('  ⚠️  Aetna carrier not found, skipping extraction');
          return;
        }
        
        const aetnaCarrierId = carrierResult.rows[0].id;
        
        // Extract plan information from the document text
        const text = data.text || '';
        
        // Parse state-by-state plan information
        const stateMatches = text.match(/State Contract PBP Dental Plan Name.*?(?=State Contract PBP|$)/gs);
        
        if (stateMatches) {
          for (const stateSection of stateMatches) {
            const stateMatch = stateSection.match(/^(\w+)(?:\s+\(continued\))?/);
            if (!stateMatch) continue;
            
            const state = stateMatch[1];
            
            // Extract individual plan entries
            const planMatches = stateSection.match(/([A-Z]{2})\s+([HRG]\d+)\s+(\d+)\s+([^$]+?)\s+\$?([\d,]+|N\/A)\s+(Yes|No|Must use)/g);
            
            if (planMatches) {
              for (const planMatch of planMatches) {
                const parts = planMatch.split(/\s+/);
                if (parts.length >= 6) {
                  const stateCode = parts[0];
                  const contract = parts[1];
                  const pbp = parts[2];
                  
                  // Extract plan name (everything between PBP and maximum)
                  const planNameMatch = planMatch.match(/\d+\s+([^$]+?)\s+\$?[\d,]+/);
                  const planName = planNameMatch ? planNameMatch[1].trim() : 'Unknown Plan';
                  
                  // Extract maximum benefit
                  const maxMatch = planMatch.match(/\$?([\d,]+|N\/A)/);
                  const maximum = maxMatch ? maxMatch[1] : 'N/A';
                  
                  // Extract out of network coverage
                  const oonMatch = planMatch.match(/(Yes|No|Must use.*?)$/);
                  const outOfNetwork = oonMatch ? oonMatch[1] : 'Unknown';
                  
                  const plan: MedicareAdvantagePlan = {
                    carrier_id: aetnaCarrierId,
                    plan_name: planName,
                    states_covered: [stateCode],
                    contract_number: contract,
                    pbp_number: pbp,
                    maximum_benefit: maximum === 'N/A' ? null : maximum,
                    out_of_network_coverage: outOfNetwork.toLowerCase().includes('yes'),
                    effective_date: '2025-01-01',
                    notes: outOfNetwork.includes('Must use') ? outOfNetwork : null
                  };
                  
                  plans.push(plan);
                }
              }
            }
          }
        }
        
        console.log(`  📊 Extracted ${plans.length} Medicare Advantage plans`);
        
        // Store plans in database
        await this.storePlans(client, plans);
        
      } finally {
        await client.end();
      }
      
    } catch (error) {
      console.log('  ⚠️  Error processing Medicare document:', error);
    }
  }

  private async storePlans(client: Client, plans: MedicareAdvantagePlan[]) {
    console.log('💾 Storing Medicare Advantage plans in database...');
    
    let insertedCount = 0;
    let skippedCount = 0;
    
    for (const plan of plans) {
      try {
        // Check if plan already exists
        const existingResult = await client.query(
          'SELECT id FROM medicare_advantage_plans WHERE carrier_id = $1 AND plan_name = $2',
          [plan.carrier_id, plan.plan_name]
        );
        
        if (existingResult.rows.length === 0) {
          // Insert new plan
          await client.query(`
            INSERT INTO medicare_advantage_plans (
              carrier_id, plan_name, states_covered, effective_date, notes, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
          `, [
            plan.carrier_id,
            plan.plan_name,
            plan.states_covered.join(', '),
            plan.effective_date,
            this.buildPlanNotes(plan)
          ]);
          insertedCount++;
        } else {
          // Update existing plan
          await client.query(`
            UPDATE medicare_advantage_plans 
            SET states_covered = $3,
                effective_date = $4,
                notes = $5,
                updated_at = NOW()
            WHERE carrier_id = $1 AND plan_name = $2
          `, [
            plan.carrier_id,
            plan.plan_name,
            plan.states_covered.join(', '),
            plan.effective_date,
            this.buildPlanNotes(plan)
          ]);
          skippedCount++;
        }
        
      } catch (error) {
        console.log(`  ❌ Error storing plan ${plan.plan_name}:`, error);
        skippedCount++;
      }
    }
    
    console.log(`  ✅ Inserted ${insertedCount} plans, updated ${skippedCount} existing plans`);
  }

  private buildPlanNotes(plan: MedicareAdvantagePlan): string {
    const notes = [];
    
    if (plan.contract_number) {
      notes.push(`Contract: ${plan.contract_number}`);
    }
    
    if (plan.pbp_number) {
      notes.push(`PBP: ${plan.pbp_number}`);
    }
    
    if (plan.maximum_benefit) {
      notes.push(`Maximum: $${plan.maximum_benefit}`);
    }
    
    if (plan.out_of_network_coverage !== undefined) {
      notes.push(`Out of Network: ${plan.out_of_network_coverage ? 'Yes' : 'No'}`);
    }
    
    if (plan.notes) {
      notes.push(plan.notes);
    }
    
    return notes.join(' | ');
  }
}

// Run the script
if (require.main === module) {
  const extractor = new MedicareAdvantageExtractor();
  extractor.run().catch(console.error);
}

export default MedicareAdvantageExtractor;
