#!/usr/bin/env ts-node

import { Client } from 'pg';
import { createClient } from '@supabase/supabase-js';
import OpenAI from 'openai';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface DocumentMetadata {
  id: string;
  filename: string;
  insurer?: string;
  procedure_code?: string;
  document_type: 'guideline' | 'claim_sample' | 'policy' | 'reference' | 'other';
  upload_date: Date;
  file_size: number;
  file_path: string;
  storage_path?: string;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
}

interface DocumentChunk {
  id: string;
  document_id: string;
  chunk_index: number;
  content: string;
  embedding?: number[];
  metadata: Record<string, any>;
}

class DocumentIngestionPipeline {
  private supabase: any;
  private openai: OpenAI;
  private pgClient: Client;

  constructor() {
    // Initialize Supabase client only if credentials are available
    if (process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      this.supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_ROLE_KEY
      );
    } else {
      console.log('⚠️  Supabase credentials not found, storage upload will be skipped');
      this.supabase = null;
    }

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || 'dummy-key',
    });

    this.pgClient = new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING,
      ssl: { rejectUnauthorized: false },
    });
  }

  async run() {
    console.log('🚀 Starting Document Ingestion Pipeline...');
    
    try {
      await this.pgClient.connect();
      await this.initializeTables();
      
      // Process documents from @Parsed directory
      await this.processExistingDocuments();
      
      console.log('✅ Document Ingestion Pipeline completed successfully!');
      
    } catch (error) {
      console.error('❌ Error in ingestion pipeline:', error);
      process.exit(1);
    } finally {
      await this.pgClient.end();
    }
  }

  private async initializeTables() {
    console.log('🗄️ Initializing ingestion pipeline tables...');
    
    // Create documents table
    await this.pgClient.query(`
      CREATE TABLE IF NOT EXISTS ingested_documents (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        filename TEXT NOT NULL,
        insurer TEXT,
        procedure_code TEXT,
        document_type TEXT NOT NULL CHECK (document_type IN ('guideline', 'claim_sample', 'policy', 'reference', 'other')),
        upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        file_size BIGINT,
        file_path TEXT,
        processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
        storage_path TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create document chunks table with vector embeddings
    await this.pgClient.query(`
      CREATE TABLE IF NOT EXISTS document_chunks (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        document_id UUID REFERENCES ingested_documents(id) ON DELETE CASCADE,
        chunk_index INTEGER NOT NULL,
        content TEXT NOT NULL,
        embedding vector(1536),
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes for efficient querying
    await this.pgClient.query(`
      CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id ON document_chunks(document_id);
      CREATE INDEX IF NOT EXISTS idx_document_chunks_embedding ON document_chunks USING ivfflat (embedding vector_cosine_ops);
      CREATE INDEX IF NOT EXISTS idx_ingested_documents_type ON ingested_documents(document_type);
      CREATE INDEX IF NOT EXISTS idx_ingested_documents_insurer ON ingested_documents(insurer);
    `);

    console.log('  ✅ Tables and indexes created');
  }

  private async processExistingDocuments() {
    console.log('📁 Processing existing documents from @Parsed directory...');
    
    const parsedDir = path.join(process.cwd(), '@Parsed');
    
    if (!fs.existsSync(parsedDir)) {
      console.log('  ⚠️  @Parsed directory not found, skipping existing documents');
      return;
    }
    
    const files = fs.readdirSync(parsedDir);
    let processedCount = 0;
    
    for (const file of files) {
      if (!file.endsWith('.json')) continue;
      
      try {
        console.log(`  📄 Processing ${file}...`);
        
        const filePath = path.join(parsedDir, file);
        const content = fs.readFileSync(filePath, 'utf-8');
        const data = JSON.parse(content);
        
        // Extract metadata from filename and content
        const metadata = this.extractMetadata(file, data);
        
        // Upload to Supabase Storage
        const storagePath = await this.uploadToStorage(filePath, file);
        
        // Store document metadata
        const documentId = await this.storeDocumentMetadata({
          ...metadata,
          file_path: filePath,
          storage_path: storagePath
        });
        
        // Process and chunk the document
        await this.chunkAndEmbedDocument(documentId, data);
        
        // Update processing status
        await this.updateDocumentStatus(documentId, 'completed');
        
        processedCount++;
        console.log(`    ✅ Processed ${file}`);
        
      } catch (error) {
        console.log(`    ❌ Error processing ${file}:`, error);
      }
    }
    
    console.log(`  📊 Processed ${processedCount} documents`);
  }

  private extractMetadata(filename: string, data: any): Partial<DocumentMetadata> {
    const metadata: Partial<DocumentMetadata> = {
      id: uuidv4(),
      filename,
      document_type: 'guideline', // Default type
      upload_date: new Date(),
      file_size: JSON.stringify(data).length,
      processing_status: 'processing'
    };

    // Extract insurer from filename or content
    const lowerFilename = filename.toLowerCase();
    
    if (lowerFilename.includes('delta')) {
      metadata.insurer = 'Delta Dental';
    } else if (lowerFilename.includes('aetna')) {
      metadata.insurer = 'Aetna';
    } else if (lowerFilename.includes('cigna')) {
      metadata.insurer = 'Cigna';
    } else if (lowerFilename.includes('humana')) {
      metadata.insurer = 'Humana';
    } else if (lowerFilename.includes('bcbs') || lowerFilename.includes('blue cross')) {
      metadata.insurer = 'Blue Cross Blue Shield';
    } else if (lowerFilename.includes('medicare')) {
      metadata.insurer = 'Medicare';
    }

    // Determine document type
    if (lowerFilename.includes('guideline') || lowerFilename.includes('guide')) {
      metadata.document_type = 'guideline';
    } else if (lowerFilename.includes('policy')) {
      metadata.document_type = 'policy';
    } else if (lowerFilename.includes('claim')) {
      metadata.document_type = 'claim_sample';
    } else if (lowerFilename.includes('reference')) {
      metadata.document_type = 'reference';
    }

    // Extract procedure codes if present
    const procedureMatch = filename.match(/D\d{4}/i);
    if (procedureMatch) {
      metadata.procedure_code = procedureMatch[0].toUpperCase();
    }

    return metadata;
  }

  private async uploadToStorage(filePath: string, filename: string): Promise<string> {
    if (!this.supabase) {
      console.log(`    ⚠️  Supabase not available, skipping storage upload for ${filename}`);
      return '';
    }

    try {
      const fileBuffer = fs.readFileSync(filePath);
      const storagePath = `ingested-documents/${Date.now()}-${filename}`;

      const { data, error } = await this.supabase.storage
        .from('documents')
        .upload(storagePath, fileBuffer, {
          contentType: 'application/json',
          upsert: false
        });

      if (error) {
        console.log(`    ⚠️  Storage upload failed for ${filename}, continuing without storage`);
        return '';
      }

      return storagePath;
    } catch (error) {
      console.log(`    ⚠️  Storage upload error for ${filename}:`, error);
      return '';
    }
  }

  private async storeDocumentMetadata(metadata: Partial<DocumentMetadata>): Promise<string> {
    const result = await this.pgClient.query(`
      INSERT INTO ingested_documents (
        id, filename, insurer, procedure_code, document_type, 
        upload_date, file_size, file_path, storage_path, processing_status
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING id
    `, [
      metadata.id,
      metadata.filename,
      metadata.insurer,
      metadata.procedure_code,
      metadata.document_type,
      metadata.upload_date,
      metadata.file_size,
      metadata.file_path,
      metadata.storage_path,
      metadata.processing_status
    ]);

    return result.rows[0].id;
  }

  private async chunkAndEmbedDocument(documentId: string, data: any) {
    console.log(`    🔄 Chunking and embedding document...`);
    
    let content = '';
    
    // Extract text content from different document formats
    if (data.text) {
      content = data.text;
    } else if (data.documents) {
      // Handle parsed document format
      for (const doc of data.documents) {
        if (doc.pages) {
          for (const page of doc.pages) {
            content += page.content || '';
          }
        }
      }
    } else if (typeof data === 'string') {
      content = data;
    }

    if (!content.trim()) {
      console.log(`    ⚠️  No content found in document`);
      return;
    }

    // Split content into chunks (approximately 1000 characters with overlap)
    const chunks = this.splitIntoChunks(content, 1000, 200);
    
    console.log(`    📝 Created ${chunks.length} chunks`);
    
    // Process chunks in batches to avoid rate limits
    const batchSize = 5;
    for (let i = 0; i < chunks.length; i += batchSize) {
      const batch = chunks.slice(i, i + batchSize);
      
      await Promise.all(batch.map(async (chunk, batchIndex) => {
        const chunkIndex = i + batchIndex;
        await this.processChunk(documentId, chunk, chunkIndex);
      }));
      
      // Small delay between batches
      if (i + batchSize < chunks.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    console.log(`    ✅ Embedded ${chunks.length} chunks`);
  }

  private splitIntoChunks(text: string, chunkSize: number, overlap: number): string[] {
    const chunks: string[] = [];
    let start = 0;
    
    while (start < text.length) {
      const end = Math.min(start + chunkSize, text.length);
      let chunk = text.slice(start, end);
      
      // Try to break at sentence boundaries
      if (end < text.length) {
        const lastSentence = chunk.lastIndexOf('.');
        const lastNewline = chunk.lastIndexOf('\n');
        const breakPoint = Math.max(lastSentence, lastNewline);
        
        if (breakPoint > start + chunkSize * 0.5) {
          chunk = text.slice(start, breakPoint + 1);
          start = breakPoint + 1 - overlap;
        } else {
          start = end - overlap;
        }
      } else {
        start = end;
      }
      
      if (chunk.trim()) {
        chunks.push(chunk.trim());
      }
    }
    
    return chunks;
  }

  private async processChunk(documentId: string, content: string, chunkIndex: number) {
    try {
      // Generate embedding
      const embedding = await this.generateEmbedding(content);
      
      // Store chunk with embedding
      await this.pgClient.query(`
        INSERT INTO document_chunks (
          document_id, chunk_index, content, embedding, metadata
        )
        VALUES ($1, $2, $3, $4, $5)
      `, [
        documentId,
        chunkIndex,
        content,
        JSON.stringify(embedding),
        JSON.stringify({
          length: content.length,
          word_count: content.split(/\s+/).length
        })
      ]);
      
    } catch (error) {
      console.log(`      ❌ Error processing chunk ${chunkIndex}:`, error);
    }
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: text.substring(0, 8000), // Limit to model's context window
      });
      
      return response.data[0].embedding;
      
    } catch (error) {
      console.log(`      ⚠️  Embedding generation failed, using zero vector`);
      return new Array(1536).fill(0); // Return zero vector as fallback
    }
  }

  private async updateDocumentStatus(documentId: string, status: string) {
    await this.pgClient.query(`
      UPDATE ingested_documents 
      SET processing_status = $1, updated_at = NOW() 
      WHERE id = $2
    `, [status, documentId]);
  }
}

// Run the pipeline
if (require.main === module) {
  const pipeline = new DocumentIngestionPipeline();
  pipeline.run().catch(console.error);
}

export default DocumentIngestionPipeline;
