#!/usr/bin/env ts-node

import { Client } from 'pg';
import OpenAI from 'openai';
import * as dotenv from 'dotenv';
import * as cron from 'node-cron';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface IndexMaintenanceConfig {
  reindexSchedule: string; // Cron expression
  batchSize: number;
  embeddingModel: string;
  maxRetries: number;
}

class IndexMaintenance {
  private pgClient: Client;
  private openai: OpenAI;
  private config: IndexMaintenanceConfig;

  constructor(config?: Partial<IndexMaintenanceConfig>) {
    this.pgClient = new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING,
      ssl: { rejectUnauthorized: false },
    });
    
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY!,
    });
    
    this.config = {
      reindexSchedule: '0 2 * * 0', // Weekly on Sunday at 2 AM
      batchSize: 50,
      embeddingModel: 'text-embedding-3-small',
      maxRetries: 3,
      ...config
    };
  }

  async run() {
    console.log('🔧 Starting Index Maintenance System...');
    
    try {
      await this.pgClient.connect();
      
      // Run immediate maintenance check
      await this.performMaintenance();
      
      // Schedule automated maintenance
      this.scheduleAutomatedMaintenance();
      
      console.log('✅ Index Maintenance System initialized');
      
    } catch (error) {
      console.error('❌ Error in index maintenance:', error);
      process.exit(1);
    }
  }

  private scheduleAutomatedMaintenance() {
    console.log(`📅 Scheduling automated maintenance: ${this.config.reindexSchedule}`);
    
    cron.schedule(this.config.reindexSchedule, async () => {
      console.log('🔄 Running scheduled index maintenance...');
      await this.performMaintenance();
    });
  }

  async performMaintenance() {
    console.log('🛠️ Performing index maintenance...');
    
    try {
      // 1. Check for documents with missing embeddings
      await this.reprocessMissingEmbeddings();
      
      // 2. Update embeddings for modified documents
      await this.updateModifiedDocuments();
      
      // 3. Optimize vector indexes
      await this.optimizeVectorIndexes();
      
      // 4. Clean up orphaned chunks
      await this.cleanupOrphanedChunks();
      
      // 5. Update index statistics
      await this.updateIndexStatistics();
      
      console.log('✅ Index maintenance completed');
      
    } catch (error) {
      console.error('❌ Error during maintenance:', error);
    }
  }

  private async reprocessMissingEmbeddings() {
    console.log('🔍 Checking for missing embeddings...');
    
    const result = await this.pgClient.query(`
      SELECT id, content 
      FROM document_chunks 
      WHERE embedding IS NULL 
      ORDER BY created_at DESC
      LIMIT $1
    `, [this.config.batchSize]);
    
    if (result.rows.length === 0) {
      console.log('  ✅ No missing embeddings found');
      return;
    }
    
    console.log(`  📝 Processing ${result.rows.length} chunks with missing embeddings`);
    
    for (const row of result.rows) {
      try {
        const embedding = await this.generateEmbedding(row.content);
        
        await this.pgClient.query(`
          UPDATE document_chunks 
          SET embedding = $1, updated_at = NOW() 
          WHERE id = $2
        `, [JSON.stringify(embedding), row.id]);
        
      } catch (error) {
        console.log(`    ❌ Failed to process chunk ${row.id}:`, error);
      }
    }
    
    console.log('  ✅ Missing embeddings processed');
  }

  private async updateModifiedDocuments() {
    console.log('🔄 Checking for modified documents...');
    
    // Find documents that have been updated but chunks haven't been re-embedded
    const result = await this.pgClient.query(`
      SELECT DISTINCT d.id, d.filename
      FROM ingested_documents d
      JOIN document_chunks c ON d.id = c.document_id
      WHERE d.updated_at > c.updated_at
      AND d.processing_status = 'completed'
      LIMIT $1
    `, [this.config.batchSize]);
    
    if (result.rows.length === 0) {
      console.log('  ✅ No modified documents found');
      return;
    }
    
    console.log(`  📄 Found ${result.rows.length} documents with outdated embeddings`);
    
    for (const doc of result.rows) {
      try {
        await this.reprocessDocumentChunks(doc.id);
        console.log(`    ✅ Reprocessed ${doc.filename}`);
      } catch (error) {
        console.log(`    ❌ Failed to reprocess ${doc.filename}:`, error);
      }
    }
  }

  private async reprocessDocumentChunks(documentId: string) {
    // Get all chunks for the document
    const chunks = await this.pgClient.query(`
      SELECT id, content 
      FROM document_chunks 
      WHERE document_id = $1 
      ORDER BY chunk_index
    `, [documentId]);
    
    // Re-generate embeddings for each chunk
    for (const chunk of chunks.rows) {
      const embedding = await this.generateEmbedding(chunk.content);
      
      await this.pgClient.query(`
        UPDATE document_chunks 
        SET embedding = $1, updated_at = NOW() 
        WHERE id = $2
      `, [JSON.stringify(embedding), chunk.id]);
    }
  }

  private async optimizeVectorIndexes() {
    console.log('⚡ Optimizing vector indexes...');
    
    try {
      // Analyze table statistics
      await this.pgClient.query('ANALYZE document_chunks;');
      
      // Reindex vector indexes if needed
      const indexStats = await this.pgClient.query(`
        SELECT schemaname, tablename, indexname, idx_tup_read, idx_tup_fetch
        FROM pg_stat_user_indexes 
        WHERE tablename = 'document_chunks'
        AND indexname LIKE '%embedding%'
      `);
      
      for (const index of indexStats.rows) {
        if (index.idx_tup_read > 10000) { // Threshold for reindexing
          console.log(`  🔄 Reindexing ${index.indexname}...`);
          await this.pgClient.query(`REINDEX INDEX ${index.indexname};`);
        }
      }
      
      console.log('  ✅ Vector indexes optimized');
      
    } catch (error) {
      console.log('  ⚠️  Index optimization failed:', error);
    }
  }

  private async cleanupOrphanedChunks() {
    console.log('🧹 Cleaning up orphaned chunks...');
    
    const result = await this.pgClient.query(`
      DELETE FROM document_chunks 
      WHERE document_id NOT IN (
        SELECT id FROM ingested_documents
      )
      RETURNING id
    `);
    
    if (result.rows.length > 0) {
      console.log(`  🗑️  Removed ${result.rows.length} orphaned chunks`);
    } else {
      console.log('  ✅ No orphaned chunks found');
    }
  }

  private async updateIndexStatistics() {
    console.log('📊 Updating index statistics...');
    
    try {
      // Create or update maintenance log table
      await this.pgClient.query(`
        CREATE TABLE IF NOT EXISTS index_maintenance_log (
          id SERIAL PRIMARY KEY,
          maintenance_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          total_documents INTEGER,
          total_chunks INTEGER,
          chunks_with_embeddings INTEGER,
          avg_chunk_size NUMERIC,
          maintenance_duration_ms INTEGER
        );
      `);
      
      const startTime = Date.now();
      
      // Gather statistics
      const stats = await this.pgClient.query(`
        SELECT 
          (SELECT COUNT(*) FROM ingested_documents) as total_documents,
          (SELECT COUNT(*) FROM document_chunks) as total_chunks,
          (SELECT COUNT(*) FROM document_chunks WHERE embedding IS NOT NULL) as chunks_with_embeddings,
          (SELECT AVG(LENGTH(content)) FROM document_chunks) as avg_chunk_size
      `);
      
      const duration = Date.now() - startTime;
      
      // Log maintenance run
      await this.pgClient.query(`
        INSERT INTO index_maintenance_log (
          total_documents, total_chunks, chunks_with_embeddings, 
          avg_chunk_size, maintenance_duration_ms
        )
        VALUES ($1, $2, $3, $4, $5)
      `, [
        stats.rows[0].total_documents,
        stats.rows[0].total_chunks,
        stats.rows[0].chunks_with_embeddings,
        stats.rows[0].avg_chunk_size,
        duration
      ]);
      
      console.log('  📈 Statistics updated:');
      console.log(`    Documents: ${stats.rows[0].total_documents}`);
      console.log(`    Chunks: ${stats.rows[0].total_chunks}`);
      console.log(`    With embeddings: ${stats.rows[0].chunks_with_embeddings}`);
      console.log(`    Avg chunk size: ${Math.round(stats.rows[0].avg_chunk_size)} chars`);
      
    } catch (error) {
      console.log('  ⚠️  Statistics update failed:', error);
    }
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    let retries = 0;
    
    while (retries < this.config.maxRetries) {
      try {
        const response = await this.openai.embeddings.create({
          model: this.config.embeddingModel,
          input: text.substring(0, 8000),
        });
        
        return response.data[0].embedding;
        
      } catch (error) {
        retries++;
        if (retries >= this.config.maxRetries) {
          throw error;
        }
        
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
      }
    }
    
    throw new Error('Max retries exceeded for embedding generation');
  }

  // Manual maintenance methods
  async forceReindex() {
    console.log('🔄 Force reindexing all embeddings...');
    
    const chunks = await this.pgClient.query(`
      SELECT id, content FROM document_chunks ORDER BY created_at
    `);
    
    console.log(`📝 Reprocessing ${chunks.rows.length} chunks...`);
    
    for (let i = 0; i < chunks.rows.length; i += this.config.batchSize) {
      const batch = chunks.rows.slice(i, i + this.config.batchSize);
      
      await Promise.all(batch.map(async (chunk) => {
        try {
          const embedding = await this.generateEmbedding(chunk.content);
          await this.pgClient.query(`
            UPDATE document_chunks 
            SET embedding = $1, updated_at = NOW() 
            WHERE id = $2
          `, [JSON.stringify(embedding), chunk.id]);
        } catch (error) {
          console.log(`  ❌ Failed to reindex chunk ${chunk.id}`);
        }
      }));
      
      console.log(`  ✅ Processed batch ${Math.floor(i / this.config.batchSize) + 1}`);
      
      // Rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    console.log('✅ Force reindex completed');
  }

  async getMaintenanceStatus() {
    const result = await this.pgClient.query(`
      SELECT 
        maintenance_date,
        total_documents,
        total_chunks,
        chunks_with_embeddings,
        maintenance_duration_ms
      FROM index_maintenance_log 
      ORDER BY maintenance_date DESC 
      LIMIT 5
    `);
    
    return result.rows;
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];
  const maintenance = new IndexMaintenance();
  
  switch (command) {
    case 'run':
      maintenance.run().catch(console.error);
      break;
    case 'force-reindex':
      maintenance.pgClient.connect().then(() => {
        maintenance.forceReindex().catch(console.error);
      });
      break;
    case 'status':
      maintenance.pgClient.connect().then(async () => {
        const status = await maintenance.getMaintenanceStatus();
        console.table(status);
        process.exit(0);
      });
      break;
    default:
      console.log('Usage: ts-node index-maintenance.ts [run|force-reindex|status]');
      process.exit(1);
  }
}

export default IndexMaintenance;
