# Document Ingestion Pipeline

A comprehensive system for uploading, processing, and maintaining dental insurance documents with vector embeddings for semantic search.

## Overview

The ingestion pipeline consists of three main components:

1. **Document Ingestion Pipeline** - Processes documents, creates chunks, and generates embeddings
2. **Index Maintenance** - Automated system for maintaining and optimizing vector indexes
3. **Upload API** - REST API for document upload and management

## Features

### Document Upload & Storage
- ✅ Upload documents via REST API
- ✅ Store files in Supabase Storage
- ✅ Extract metadata (insurer, procedure codes, document type)
- ✅ Support for PDF, JSON, TXT, and DOC files
- ✅ Batch upload capabilities

### Chunking & Embedding
- ✅ Intelligent text chunking with overlap
- ✅ Sentence boundary detection
- ✅ OpenAI embeddings (text-embedding-3-small)
- ✅ Vector storage using pgvector
- ✅ Metadata preservation

### Index Maintenance
- ✅ Automated reindexing on schedule
- ✅ Missing embedding detection and repair
- ✅ Orphaned chunk cleanup
- ✅ Vector index optimization
- ✅ Performance monitoring

## Database Schema

### ingested_documents
```sql
CREATE TABLE ingested_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  filename TEXT NOT NULL,
  insurer TEXT,
  procedure_code TEXT,
  document_type TEXT NOT NULL CHECK (document_type IN ('guideline', 'claim_sample', 'policy', 'reference', 'other')),
  upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  file_size BIGINT,
  file_path TEXT,
  processing_status TEXT DEFAULT 'pending',
  storage_path TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### document_chunks
```sql
CREATE TABLE document_chunks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES ingested_documents(id) ON DELETE CASCADE,
  chunk_index INTEGER NOT NULL,
  content TEXT NOT NULL,
  embedding vector(1536),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Usage

### 1. Initial Setup

Install dependencies:
```bash
npm install express multer @supabase/supabase-js openai pg node-cron uuid
npm install --save-dev @types/express @types/multer @types/node @types/uuid
```

Set up environment variables in `.env.development`:
```env
POSTGRES_CONNECTION_STRING=your_postgres_connection
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
OPENAI_API_KEY=your_openai_api_key
```

### 2. Run Initial Ingestion

Process existing documents from @Parsed directory:
```bash
npx ts-node scripts/ingestion-pipeline/document-ingestion-pipeline.ts
```

### 3. Start Upload API

Start the REST API server:
```bash
npx ts-node scripts/ingestion-pipeline/upload-api.ts
```

The API will be available at `http://localhost:3001`

### 4. Set Up Index Maintenance

Run automated maintenance:
```bash
npx ts-node scripts/ingestion-pipeline/index-maintenance.ts run
```

Or run specific maintenance tasks:
```bash
# Force reindex all embeddings
npx ts-node scripts/ingestion-pipeline/index-maintenance.ts force-reindex

# Check maintenance status
npx ts-node scripts/ingestion-pipeline/index-maintenance.ts status
```

## API Endpoints

### Upload Single Document
```bash
curl -X POST http://localhost:3001/upload \
  -F "document=@path/to/file.pdf" \
  -F "insurer=Delta Dental" \
  -F "document_type=guideline" \
  -F "procedure_code=D1110"
```

### Upload Multiple Documents
```bash
curl -X POST http://localhost:3001/upload/batch \
  -F "documents=@file1.pdf" \
  -F "documents=@file2.pdf" \
  -F "insurer=Aetna" \
  -F "document_type=policy"
```

### Check Upload Status
```bash
curl http://localhost:3001/upload/{document_id}/status
```

### List Documents
```bash
curl "http://localhost:3001/documents?page=1&limit=20"
```

### Search Documents
```bash
curl -X POST http://localhost:3001/search \
  -H "Content-Type: application/json" \
  -d '{"query": "dental cleaning procedures", "limit": 10}'
```

### Reprocess Document
```bash
curl -X POST http://localhost:3001/documents/{document_id}/reprocess
```

### Delete Document
```bash
curl -X DELETE http://localhost:3001/documents/{document_id}
```

## Configuration

### Index Maintenance Configuration
```typescript
const config = {
  reindexSchedule: '0 2 * * 0', // Weekly on Sunday at 2 AM
  batchSize: 50,
  embeddingModel: 'text-embedding-3-small',
  maxRetries: 3
};
```

### Chunking Parameters
- **Chunk Size**: 1000 characters
- **Overlap**: 200 characters
- **Boundary Detection**: Sentence and paragraph breaks
- **Max Context**: 8000 characters for embedding

## Monitoring

### Maintenance Logs
The system maintains logs of all maintenance operations:
```sql
SELECT * FROM index_maintenance_log ORDER BY maintenance_date DESC LIMIT 10;
```

### Document Statistics
```sql
SELECT 
  document_type,
  COUNT(*) as document_count,
  AVG(file_size) as avg_file_size,
  SUM(CASE WHEN processing_status = 'completed' THEN 1 ELSE 0 END) as completed_count
FROM ingested_documents 
GROUP BY document_type;
```

### Embedding Coverage
```sql
SELECT 
  COUNT(*) as total_chunks,
  COUNT(embedding) as chunks_with_embeddings,
  ROUND(COUNT(embedding) * 100.0 / COUNT(*), 2) as coverage_percentage
FROM document_chunks;
```

## Error Handling

### Common Issues

1. **Missing Embeddings**: Automatically detected and fixed by maintenance system
2. **Storage Upload Failures**: Documents stored locally with retry mechanism
3. **Rate Limiting**: Built-in backoff and retry logic for OpenAI API
4. **Large Files**: 50MB upload limit with chunking for processing

### Troubleshooting

Check processing status:
```sql
SELECT processing_status, COUNT(*) 
FROM ingested_documents 
GROUP BY processing_status;
```

Find failed documents:
```sql
SELECT id, filename, upload_date 
FROM ingested_documents 
WHERE processing_status = 'failed';
```

## Performance Optimization

### Vector Index Tuning
```sql
-- Adjust IVFFlat parameters based on data size
CREATE INDEX idx_document_chunks_embedding 
ON document_chunks 
USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);
```

### Batch Processing
- Process embeddings in batches of 5-10 to avoid rate limits
- Use connection pooling for database operations
- Implement exponential backoff for API retries

## Security Considerations

- Use service role key for Supabase operations
- Validate file types and sizes before processing
- Sanitize metadata inputs
- Implement rate limiting on upload endpoints
- Use HTTPS in production

## Future Enhancements

- [ ] Support for additional file formats (DOCX, HTML)
- [ ] Advanced metadata extraction using AI
- [ ] Real-time processing status updates via WebSocket
- [ ] Document versioning and change tracking
- [ ] Advanced search filters and faceting
- [ ] Integration with external document sources
