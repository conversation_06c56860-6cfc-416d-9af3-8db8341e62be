#!/usr/bin/env ts-node

import express from 'express';
import multer from 'multer';
import { Client } from 'pg';
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import DocumentIngestionPipeline from './document-ingestion-pipeline';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface UploadRequest {
  insurer?: string;
  procedure_code?: string;
  document_type: 'guideline' | 'claim_sample' | 'policy' | 'reference' | 'other';
  tags?: string[];
}

class DocumentUploadAPI {
  private app: express.Application;
  private upload: multer.Multer;
  private supabase: any;
  private pgClient: Client;
  private ingestionPipeline: DocumentIngestionPipeline;

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    
    this.pgClient = new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING,
      ssl: { rejectUnauthorized: false },
    });
    
    this.ingestionPipeline = new DocumentIngestionPipeline();
  }

  private setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // Configure multer for file uploads
    this.upload = multer({
      dest: 'uploads/',
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB limit
      },
      fileFilter: (req, file, cb) => {
        // Accept PDF, JSON, TXT, and DOC files
        const allowedTypes = [
          'application/pdf',
          'application/json',
          'text/plain',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];
        
        if (allowedTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error('Invalid file type. Only PDF, JSON, TXT, and DOC files are allowed.'));
        }
      }
    });
  }

  private setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    // Upload single document
    this.app.post('/upload', this.upload.single('document'), this.handleSingleUpload.bind(this));

    // Upload multiple documents
    this.app.post('/upload/batch', this.upload.array('documents', 10), this.handleBatchUpload.bind(this));

    // Get upload status
    this.app.get('/upload/:id/status', this.getUploadStatus.bind(this));

    // List uploaded documents
    this.app.get('/documents', this.listDocuments.bind(this));

    // Search documents
    this.app.post('/search', this.searchDocuments.bind(this));

    // Reprocess document
    this.app.post('/documents/:id/reprocess', this.reprocessDocument.bind(this));

    // Delete document
    this.app.delete('/documents/:id', this.deleteDocument.bind(this));

    // Error handling
    this.app.use(this.errorHandler.bind(this));
  }

  private async handleSingleUpload(req: express.Request, res: express.Response) {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const metadata: UploadRequest = {
        insurer: req.body.insurer,
        procedure_code: req.body.procedure_code,
        document_type: req.body.document_type || 'other',
        tags: req.body.tags ? JSON.parse(req.body.tags) : []
      };

      const documentId = await this.processUploadedFile(req.file, metadata);

      res.json({
        success: true,
        document_id: documentId,
        message: 'Document uploaded and processing started'
      });

    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({ error: 'Upload failed', details: error.message });
    }
  }

  private async handleBatchUpload(req: express.Request, res: express.Response) {
    try {
      if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        return res.status(400).json({ error: 'No files uploaded' });
      }

      const metadata: UploadRequest = {
        insurer: req.body.insurer,
        procedure_code: req.body.procedure_code,
        document_type: req.body.document_type || 'other',
        tags: req.body.tags ? JSON.parse(req.body.tags) : []
      };

      const results = [];
      
      for (const file of req.files) {
        try {
          const documentId = await this.processUploadedFile(file as Express.Multer.File, metadata);
          results.push({ file: file.originalname, document_id: documentId, status: 'success' });
        } catch (error) {
          results.push({ file: file.originalname, status: 'failed', error: error.message });
        }
      }

      res.json({
        success: true,
        results,
        message: `Processed ${results.length} files`
      });

    } catch (error) {
      console.error('Batch upload error:', error);
      res.status(500).json({ error: 'Batch upload failed', details: error.message });
    }
  }

  private async processUploadedFile(file: Express.Multer.File, metadata: UploadRequest): Promise<string> {
    const documentId = uuidv4();
    
    // Upload to Supabase Storage
    const fileBuffer = fs.readFileSync(file.path);
    const storagePath = `uploads/${Date.now()}-${file.originalname}`;
    
    const { error: uploadError } = await this.supabase.storage
      .from('documents')
      .upload(storagePath, fileBuffer, {
        contentType: file.mimetype,
        upsert: false
      });

    if (uploadError) {
      throw new Error(`Storage upload failed: ${uploadError.message}`);
    }

    // Store document metadata
    await this.pgClient.query(`
      INSERT INTO ingested_documents (
        id, filename, insurer, procedure_code, document_type,
        file_size, storage_path, processing_status
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, [
      documentId,
      file.originalname,
      metadata.insurer,
      metadata.procedure_code,
      metadata.document_type,
      file.size,
      storagePath,
      'pending'
    ]);

    // Clean up temporary file
    fs.unlinkSync(file.path);

    // Start background processing
    this.processDocumentAsync(documentId, storagePath);

    return documentId;
  }

  private async processDocumentAsync(documentId: string, storagePath: string) {
    try {
      // Update status to processing
      await this.pgClient.query(`
        UPDATE ingested_documents 
        SET processing_status = 'processing', updated_at = NOW() 
        WHERE id = $1
      `, [documentId]);

      // Download file from storage
      const { data, error } = await this.supabase.storage
        .from('documents')
        .download(storagePath);

      if (error) {
        throw new Error(`Failed to download file: ${error.message}`);
      }

      // Process the document content (this would need to be adapted based on file type)
      // For now, assuming it's text-based content
      const content = await data.text();
      
      // Use the ingestion pipeline to chunk and embed
      // This is a simplified version - you'd need to adapt based on your pipeline structure
      
      // Update status to completed
      await this.pgClient.query(`
        UPDATE ingested_documents 
        SET processing_status = 'completed', updated_at = NOW() 
        WHERE id = $1
      `, [documentId]);

    } catch (error) {
      console.error(`Processing failed for document ${documentId}:`, error);
      
      // Update status to failed
      await this.pgClient.query(`
        UPDATE ingested_documents 
        SET processing_status = 'failed', updated_at = NOW() 
        WHERE id = $1
      `, [documentId]);
    }
  }

  private async getUploadStatus(req: express.Request, res: express.Response) {
    try {
      const { id } = req.params;
      
      const result = await this.pgClient.query(`
        SELECT id, filename, processing_status, upload_date, 
               (SELECT COUNT(*) FROM document_chunks WHERE document_id = $1) as chunk_count
        FROM ingested_documents 
        WHERE id = $1
      `, [id]);

      if (result.rows.length === 0) {
        return res.status(404).json({ error: 'Document not found' });
      }

      res.json(result.rows[0]);

    } catch (error) {
      console.error('Status check error:', error);
      res.status(500).json({ error: 'Status check failed' });
    }
  }

  private async listDocuments(req: express.Request, res: express.Response) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const offset = (page - 1) * limit;

      const result = await this.pgClient.query(`
        SELECT d.id, d.filename, d.insurer, d.document_type, d.processing_status,
               d.upload_date, COUNT(c.id) as chunk_count
        FROM ingested_documents d
        LEFT JOIN document_chunks c ON d.id = c.document_id
        GROUP BY d.id, d.filename, d.insurer, d.document_type, d.processing_status, d.upload_date
        ORDER BY d.upload_date DESC
        LIMIT $1 OFFSET $2
      `, [limit, offset]);

      const countResult = await this.pgClient.query('SELECT COUNT(*) FROM ingested_documents');
      const total = parseInt(countResult.rows[0].count);

      res.json({
        documents: result.rows,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });

    } catch (error) {
      console.error('List documents error:', error);
      res.status(500).json({ error: 'Failed to list documents' });
    }
  }

  private async searchDocuments(req: express.Request, res: express.Response) {
    try {
      const { query, limit = 10 } = req.body;
      
      if (!query) {
        return res.status(400).json({ error: 'Query is required' });
      }

      // This would use your existing vector search functionality
      // For now, returning a placeholder response
      res.json({
        query,
        results: [],
        message: 'Vector search functionality would be implemented here'
      });

    } catch (error) {
      console.error('Search error:', error);
      res.status(500).json({ error: 'Search failed' });
    }
  }

  private async reprocessDocument(req: express.Request, res: express.Response) {
    try {
      const { id } = req.params;
      
      // Reset processing status and trigger reprocessing
      await this.pgClient.query(`
        UPDATE ingested_documents 
        SET processing_status = 'pending', updated_at = NOW() 
        WHERE id = $1
      `, [id]);

      // Delete existing chunks
      await this.pgClient.query('DELETE FROM document_chunks WHERE document_id = $1', [id]);

      res.json({ success: true, message: 'Document reprocessing started' });

    } catch (error) {
      console.error('Reprocess error:', error);
      res.status(500).json({ error: 'Reprocessing failed' });
    }
  }

  private async deleteDocument(req: express.Request, res: express.Response) {
    try {
      const { id } = req.params;
      
      // Get document info
      const docResult = await this.pgClient.query(
        'SELECT storage_path FROM ingested_documents WHERE id = $1',
        [id]
      );

      if (docResult.rows.length === 0) {
        return res.status(404).json({ error: 'Document not found' });
      }

      // Delete from storage
      const storagePath = docResult.rows[0].storage_path;
      if (storagePath) {
        await this.supabase.storage.from('documents').remove([storagePath]);
      }

      // Delete from database (chunks will be deleted by CASCADE)
      await this.pgClient.query('DELETE FROM ingested_documents WHERE id = $1', [id]);

      res.json({ success: true, message: 'Document deleted successfully' });

    } catch (error) {
      console.error('Delete error:', error);
      res.status(500).json({ error: 'Deletion failed' });
    }
  }

  private errorHandler(error: Error, req: express.Request, res: express.Response, next: express.NextFunction) {
    console.error('API Error:', error);
    
    if (error instanceof multer.MulterError) {
      if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({ error: 'File too large' });
      }
    }
    
    res.status(500).json({ error: 'Internal server error' });
  }

  async start(port: number = 3001) {
    await this.pgClient.connect();
    
    this.app.listen(port, () => {
      console.log(`📡 Document Upload API running on port ${port}`);
      console.log(`🔗 Health check: http://localhost:${port}/health`);
      console.log(`📤 Upload endpoint: http://localhost:${port}/upload`);
    });
  }
}

// Start the API server
if (require.main === module) {
  const api = new DocumentUploadAPI();
  api.start().catch(console.error);
}

export default DocumentUploadAPI;
