#!/usr/bin/env ts-node

import { Client } from 'pg';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

interface CarrierAlias {
  carrier_id: number;
  alias_name: string;
}

class CarrierAliasManager {
  private createClient(): Client {
    return new Client({
      connectionString: process.env.POSTGRES_CONNECTION_STRING,
      ssl: { rejectUnauthorized: false },
    });
  }

  async run() {
    console.log('🏷️ Creating comprehensive carrier aliases...');
    
    try {
      await this.createBCBSAliases();
      await this.createDeltaDentalAliases();
      await this.createAetnaAliases();
      await this.createCignaAliases();
      await this.createHumanaAliases();
      await this.createUnitedAliases();
      await this.createAnthemAliases();
      await this.createOtherCommonAliases();
      
      console.log('✅ Comprehensive carrier aliases created successfully!');
      
    } catch (error) {
      console.error('❌ Error creating carrier aliases:', error);
      process.exit(1);
    }
  }

  private async createBCBSAliases() {
    console.log('🔵 Creating Blue Cross Blue Shield aliases...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      // Get all BCBS carriers
      const bcbsQuery = `
        SELECT id, carrier_name 
        FROM insurance_carriers 
        WHERE carrier_name ILIKE '%blue cross%' OR carrier_name ILIKE '%bcbs%'
        ORDER BY carrier_name
      `;
      
      const bcbsCarriers = await client.query(bcbsQuery);
      const aliases: CarrierAlias[] = [];
      
      for (const carrier of bcbsCarriers.rows) {
        const name = carrier.carrier_name;
        const id = carrier.id;
        
        // Common BCBS aliases
        aliases.push(
          { carrier_id: id, alias_name: 'BCBS' },
          { carrier_id: id, alias_name: 'Blue Cross' },
          { carrier_id: id, alias_name: 'Blue Shield' },
          { carrier_id: id, alias_name: 'BC/BS' },
          { carrier_id: id, alias_name: 'BCBS Plan' }
        );
        
        // State-specific aliases
        if (name.includes('Massachusetts')) {
          aliases.push(
            { carrier_id: id, alias_name: 'BCBS MA' },
            { carrier_id: id, alias_name: 'Blue Cross MA' },
            { carrier_id: id, alias_name: 'BCBSMA' },
            { carrier_id: id, alias_name: 'Massachusetts Blue Cross' }
          );
        }
        
        if (name.includes('California')) {
          aliases.push(
            { carrier_id: id, alias_name: 'BCBS CA' },
            { carrier_id: id, alias_name: 'Blue Cross CA' },
            { carrier_id: id, alias_name: 'California Blue Cross' }
          );
        }
        
        if (name.includes('Texas')) {
          aliases.push(
            { carrier_id: id, alias_name: 'BCBS TX' },
            { carrier_id: id, alias_name: 'Blue Cross TX' },
            { carrier_id: id, alias_name: 'Texas Blue Cross' }
          );
        }
        
        if (name.includes('Florida')) {
          aliases.push(
            { carrier_id: id, alias_name: 'BCBS FL' },
            { carrier_id: id, alias_name: 'Blue Cross FL' },
            { carrier_id: id, alias_name: 'Florida Blue' }
          );
        }
        
        if (name.includes('New York')) {
          aliases.push(
            { carrier_id: id, alias_name: 'BCBS NY' },
            { carrier_id: id, alias_name: 'Blue Cross NY' },
            { carrier_id: id, alias_name: 'Empire Blue Cross' }
          );
        }
        
        // Anthem-related aliases
        if (name.includes('Anthem')) {
          aliases.push(
            { carrier_id: id, alias_name: 'Anthem' },
            { carrier_id: id, alias_name: 'Anthem BCBS' },
            { carrier_id: id, alias_name: 'Anthem Blue Cross' }
          );
        }
        
        // FEP aliases
        if (name.includes('FEP') || name.includes('Federal')) {
          aliases.push(
            { carrier_id: id, alias_name: 'FEP' },
            { carrier_id: id, alias_name: 'Federal Employee Program' },
            { carrier_id: id, alias_name: 'FEP Blue Dental' }
          );
        }
      }
      
      await this.insertAliases(client, aliases);
      console.log(`  ✅ Created ${aliases.length} BCBS aliases`);
      
    } finally {
      await client.end();
    }
  }

  private async createDeltaDentalAliases() {
    console.log('🦷 Creating Delta Dental aliases...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      const deltaQuery = `
        SELECT id, carrier_name 
        FROM insurance_carriers 
        WHERE carrier_name ILIKE '%delta%'
        ORDER BY carrier_name
      `;
      
      const deltaCarriers = await client.query(deltaQuery);
      const aliases: CarrierAlias[] = [];
      
      for (const carrier of deltaCarriers.rows) {
        const name = carrier.carrier_name;
        const id = carrier.id;
        
        // Common Delta aliases
        aliases.push(
          { carrier_id: id, alias_name: 'Delta' },
          { carrier_id: id, alias_name: 'DD' },
          { carrier_id: id, alias_name: 'Delta Dental Plans' },
          { carrier_id: id, alias_name: 'Delta Dental Insurance' }
        );
        
        // AARP Delta
        if (name.includes('AARP')) {
          aliases.push(
            { carrier_id: id, alias_name: 'AARP Delta' },
            { carrier_id: id, alias_name: 'AARP Dental' }
          );
        }
        
        // State abbreviations
        const stateAbbrevs = {
          'California': 'CA', 'Texas': 'TX', 'Florida': 'FL', 'New York': 'NY',
          'Illinois': 'IL', 'Pennsylvania': 'PA', 'Ohio': 'OH', 'Georgia': 'GA',
          'Michigan': 'MI', 'North Carolina': 'NC', 'New Jersey': 'NJ'
        };
        
        for (const [state, abbrev] of Object.entries(stateAbbrevs)) {
          if (name.includes(state) || name.includes(abbrev)) {
            aliases.push(
              { carrier_id: id, alias_name: `Delta ${abbrev}` },
              { carrier_id: id, alias_name: `DD ${abbrev}` }
            );
          }
        }
      }
      
      await this.insertAliases(client, aliases);
      console.log(`  ✅ Created ${aliases.length} Delta Dental aliases`);
      
    } finally {
      await client.end();
    }
  }

  private async createAetnaAliases() {
    console.log('🏥 Creating Aetna aliases...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      const aetnaQuery = `
        SELECT id, carrier_name 
        FROM insurance_carriers 
        WHERE carrier_name ILIKE '%aetna%'
        ORDER BY carrier_name
      `;
      
      const aetnaCarriers = await client.query(aetnaQuery);
      const aliases: CarrierAlias[] = [];
      
      for (const carrier of aetnaCarriers.rows) {
        const id = carrier.id;
        
        aliases.push(
          { carrier_id: id, alias_name: 'Aetna' },
          { carrier_id: id, alias_name: 'Aetna Dental' },
          { carrier_id: id, alias_name: 'Aetna Inc' },
          { carrier_id: id, alias_name: 'Aetna Insurance' }
        );
      }
      
      await this.insertAliases(client, aliases);
      console.log(`  ✅ Created ${aliases.length} Aetna aliases`);
      
    } finally {
      await client.end();
    }
  }

  private async createCignaAliases() {
    console.log('🚬 Creating Cigna aliases...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      const cignaQuery = `
        SELECT id, carrier_name 
        FROM insurance_carriers 
        WHERE carrier_name ILIKE '%cigna%'
        ORDER BY carrier_name
      `;
      
      const cignaCarriers = await client.query(cignaQuery);
      const aliases: CarrierAlias[] = [];
      
      for (const carrier of cignaCarriers.rows) {
        const id = carrier.id;
        
        aliases.push(
          { carrier_id: id, alias_name: 'Cigna' },
          { carrier_id: id, alias_name: 'CIGNA' },
          { carrier_id: id, alias_name: 'Cigna Dental' },
          { carrier_id: id, alias_name: 'Connecticut General' }
        );
      }
      
      await this.insertAliases(client, aliases);
      console.log(`  ✅ Created ${aliases.length} Cigna aliases`);
      
    } finally {
      await client.end();
    }
  }

  private async createHumanaAliases() {
    console.log('👥 Creating Humana aliases...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      const humanaQuery = `
        SELECT id, carrier_name 
        FROM insurance_carriers 
        WHERE carrier_name ILIKE '%humana%'
        ORDER BY carrier_name
      `;
      
      const humanaCarriers = await client.query(humanaQuery);
      const aliases: CarrierAlias[] = [];
      
      for (const carrier of humanaCarriers.rows) {
        const id = carrier.id;
        
        aliases.push(
          { carrier_id: id, alias_name: 'Humana' },
          { carrier_id: id, alias_name: 'Humana Dental' },
          { carrier_id: id, alias_name: 'Humana Inc' },
          { carrier_id: id, alias_name: 'CompBenefits' }
        );
      }
      
      await this.insertAliases(client, aliases);
      console.log(`  ✅ Created ${aliases.length} Humana aliases`);
      
    } finally {
      await client.end();
    }
  }

  private async createUnitedAliases() {
    console.log('🇺🇸 Creating United Healthcare aliases...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      const unitedQuery = `
        SELECT id, carrier_name 
        FROM insurance_carriers 
        WHERE carrier_name ILIKE '%united%'
        ORDER BY carrier_name
      `;
      
      const unitedCarriers = await client.query(unitedQuery);
      const aliases: CarrierAlias[] = [];
      
      for (const carrier of unitedCarriers.rows) {
        const name = carrier.carrier_name;
        const id = carrier.id;
        
        if (name.includes('Healthcare')) {
          aliases.push(
            { carrier_id: id, alias_name: 'United' },
            { carrier_id: id, alias_name: 'UHC' },
            { carrier_id: id, alias_name: 'United Healthcare' },
            { carrier_id: id, alias_name: 'United Health' }
          );
        }
        
        if (name.includes('Concordia')) {
          aliases.push(
            { carrier_id: id, alias_name: 'United Concordia' },
            { carrier_id: id, alias_name: 'Concordia' },
            { carrier_id: id, alias_name: 'UC' }
          );
        }
      }
      
      await this.insertAliases(client, aliases);
      console.log(`  ✅ Created ${aliases.length} United aliases`);
      
    } finally {
      await client.end();
    }
  }

  private async createAnthemAliases() {
    console.log('🎵 Creating Anthem aliases...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      const anthemQuery = `
        SELECT id, carrier_name 
        FROM insurance_carriers 
        WHERE carrier_name ILIKE '%anthem%' AND carrier_name NOT ILIKE '%blue cross%'
        ORDER BY carrier_name
      `;
      
      const anthemCarriers = await client.query(anthemQuery);
      const aliases: CarrierAlias[] = [];
      
      for (const carrier of anthemCarriers.rows) {
        const id = carrier.id;
        
        aliases.push(
          { carrier_id: id, alias_name: 'Anthem' },
          { carrier_id: id, alias_name: 'Anthem Inc' },
          { carrier_id: id, alias_name: 'Anthem Dental' }
        );
      }
      
      await this.insertAliases(client, aliases);
      console.log(`  ✅ Created ${aliases.length} Anthem aliases`);
      
    } finally {
      await client.end();
    }
  }

  private async createOtherCommonAliases() {
    console.log('🏢 Creating other common carrier aliases...');
    
    const client = this.createClient();
    await client.connect();
    
    try {
      const aliases: CarrierAlias[] = [];
      
      // Guardian aliases
      const guardianResult = await client.query(`
        SELECT id FROM insurance_carriers WHERE carrier_name ILIKE '%guardian%' LIMIT 1
      `);
      if (guardianResult.rows.length > 0) {
        const id = guardianResult.rows[0].id;
        aliases.push(
          { carrier_id: id, alias_name: 'Guardian' },
          { carrier_id: id, alias_name: 'Guardian Life' },
          { carrier_id: id, alias_name: 'Guardian Dental' }
        );
      }
      
      // MetLife aliases
      const metlifeResult = await client.query(`
        SELECT id FROM insurance_carriers WHERE carrier_name ILIKE '%metlife%' LIMIT 1
      `);
      if (metlifeResult.rows.length > 0) {
        const id = metlifeResult.rows[0].id;
        aliases.push(
          { carrier_id: id, alias_name: 'MetLife' },
          { carrier_id: id, alias_name: 'Met Life' },
          { carrier_id: id, alias_name: 'Metropolitan Life' }
        );
      }
      
      await this.insertAliases(client, aliases);
      console.log(`  ✅ Created ${aliases.length} other carrier aliases`);
      
    } finally {
      await client.end();
    }
  }

  private async insertAliases(client: Client, aliases: CarrierAlias[]) {
    if (aliases.length === 0) return;

    // Insert aliases one by one to handle unique constraint conflicts
    let insertedCount = 0;
    let skippedCount = 0;

    for (const alias of aliases) {
      try {
        await client.query(`
          INSERT INTO carrier_aliases (carrier_id, alias_name, created_at, updated_at)
          VALUES ($1, $2, NOW(), NOW())
          ON CONFLICT (alias_name) DO NOTHING
        `, [alias.carrier_id, alias.alias_name]);
        insertedCount++;
      } catch (error) {
        // Skip if there's still a conflict (shouldn't happen with ON CONFLICT DO NOTHING)
        skippedCount++;
      }
    }

    console.log(`    📝 Inserted: ${insertedCount}, Skipped: ${skippedCount}`);
  }
}

// Run the script
if (require.main === module) {
  const manager = new CarrierAliasManager();
  manager.run().catch(console.error);
}

export default CarrierAliasManager;
