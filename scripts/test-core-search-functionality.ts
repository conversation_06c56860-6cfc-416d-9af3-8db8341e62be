#!/usr/bin/env ts-node

import { DatabaseUtils } from '../src/utils/database';

class CoreSearchTester {
  private dbUtils: DatabaseUtils;

  constructor() {
    this.dbUtils = DatabaseUtils.getInstance();
  }

  async testSearchGuidelines(): Promise<void> {
    console.log('🔍 Testing Guidelines Search...');
    
    try {
      const results = await this.dbUtils.searchGuidelines('crown procedure requirements', {
        limit: 3
      });

      console.log(`✅ Found ${results.length} guidelines`);
      
      if (results.length > 0) {
        console.log('📋 Sample results:');
        results.slice(0, 2).forEach((result, index) => {
          console.log(`  ${index + 1}. ${result.title} (${result.carrier})`);
          console.log(`     Similarity: ${result.similarity || 'N/A'}`);
        });
      }

    } catch (error) {
      console.error('❌ Guidelines search failed:', error);
    }
  }

  async testProcedureLookup(): Promise<void> {
    console.log('\n🦷 Testing Procedure Lookup...');
    
    try {
      const results = await this.dbUtils.lookupProcedure('crown');

      console.log(`✅ Found ${results.length} procedures`);
      
      if (results.length > 0) {
        console.log('📋 Sample results:');
        results.slice(0, 3).forEach((result, index) => {
          console.log(`  ${index + 1}. ${result.code} - ${result.name}`);
          console.log(`     Category: ${result.category}`);
        });
      }

    } catch (error) {
      console.error('❌ Procedure lookup failed:', error);
    }
  }

  async testCarrierLookup(): Promise<void> {
    console.log('\n🏥 Testing Carrier Lookup...');
    
    try {
      const result = await this.dbUtils.lookupCarrier('Delta Dental');

      if (result) {
        console.log('✅ Found carrier:');
        console.log(`  Name: ${result.name}`);
        console.log(`  Payer ID: ${result.payer_id || 'N/A'}`);
        console.log(`  Website: ${result.website || 'N/A'}`);
      } else {
        console.log('❌ No carrier found');
      }

    } catch (error) {
      console.error('❌ Carrier lookup failed:', error);
    }
  }

  async testVectorSearch(): Promise<void> {
    console.log('\n🎯 Testing Vector Search...');
    
    try {
      const results = await this.dbUtils.vectorSearch('cleaning frequency requirements', {
        limit: 3,
        similarityThreshold: 0.1
      });

      console.log(`✅ Found ${results.length} vector results`);
      
      if (results.length > 0) {
        console.log('📋 Sample results:');
        results.forEach((result, index) => {
          console.log(`  ${index + 1}. Content Type: ${result.content_type}`);
          console.log(`     Content ID: ${result.content_id}`);
          console.log(`     Similarity: ${result.similarity_score.toFixed(3)}`);
        });
      }

    } catch (error) {
      console.error('❌ Vector search failed:', error);
    }
  }

  async testSearchWithFilters(): Promise<void> {
    console.log('\n🔧 Testing Search with Filters...');
    
    try {
      // Test with carrier filter
      const deltaResults = await this.dbUtils.searchGuidelines('preventive care', {
        carrier: 'Delta',
        limit: 2
      });

      console.log(`✅ Delta Dental results: ${deltaResults.length}`);

      // Test with category filter
      const restorativeResults = await this.dbUtils.searchGuidelines('procedure requirements', {
        category: 'restorative',
        limit: 2
      });

      console.log(`✅ Restorative category results: ${restorativeResults.length}`);

    } catch (error) {
      console.error('❌ Filtered search failed:', error);
    }
  }

  async runPerformanceTest(): Promise<void> {
    console.log('\n⚡ Performance Testing...');
    
    const testCases = [
      { name: 'Guidelines Search', fn: () => this.dbUtils.searchGuidelines('crown requirements', { limit: 5 }) },
      { name: 'Procedure Lookup', fn: () => this.dbUtils.lookupProcedure('cleaning') },
      { name: 'Carrier Lookup', fn: () => this.dbUtils.lookupCarrier('Delta') },
      { name: 'Vector Search', fn: () => this.dbUtils.vectorSearch('documentation requirements', { limit: 5 }) }
    ];

    for (const testCase of testCases) {
      const startTime = Date.now();
      
      try {
        await testCase.fn();
        const duration = Date.now() - startTime;
        console.log(`⏱️  ${testCase.name}: ${duration}ms`);
      } catch (error) {
        console.log(`❌ ${testCase.name}: Failed`);
      }
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Testing Core Search Functionality');
    console.log('====================================\n');

    await this.testSearchGuidelines();
    await this.testProcedureLookup();
    await this.testCarrierLookup();
    await this.testVectorSearch();
    await this.testSearchWithFilters();
    await this.runPerformanceTest();

    console.log('\n🎉 Core search functionality testing completed!');
  }
}

// Test coverage validation functionality
async function testCoverageValidation(): Promise<void> {
  console.log('\n🏥 Testing Coverage Validation Logic...');
  
  const dbUtils = DatabaseUtils.getInstance();
  
  try {
    // Test carrier lookup
    const carrier = await dbUtils.lookupCarrier('Delta Dental');
    console.log(`✅ Carrier found: ${carrier ? 'Yes' : 'No'}`);

    // Test procedure lookup for multiple codes
    const procedureCodes = ['D1110', 'D2740', 'D0150'];
    const procedureResults = await Promise.all(
      procedureCodes.map(async (code) => {
        const procedures = await dbUtils.lookupProcedure(code);
        return {
          code,
          found: procedures.length > 0,
          name: procedures[0]?.name || 'Unknown'
        };
      })
    );

    console.log('📋 Procedure validation results:');
    procedureResults.forEach(result => {
      console.log(`  ${result.code}: ${result.found ? '✅' : '❌'} ${result.name}`);
    });

    // Test coverage guidelines search
    const coverageQuery = `Delta Dental coverage requirements for ${procedureCodes.join(' ')}`;
    const guidelines = await dbUtils.searchGuidelines(coverageQuery, {
      carrier: 'Delta',
      limit: 5
    });

    console.log(`✅ Coverage guidelines found: ${guidelines.length}`);

  } catch (error) {
    console.error('❌ Coverage validation test failed:', error);
  }
}

// Main execution
async function main() {
  const tester = new CoreSearchTester();
  await tester.runAllTests();
  await testCoverageValidation();
}

if (require.main === module) {
  main().catch(console.error);
}

export { CoreSearchTester };
