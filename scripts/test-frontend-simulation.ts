// Load environment variables first
import * as dotenv from 'dotenv';
dotenv.config({ path: '.env.development' });

import { Client } from 'pg';
import OpenAI from 'openai';

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Create PostgreSQL client
const createClient = () => new Client({
  connectionString: process.env.POSTGRES_CONNECTION_STRING,
  ssl: { rejectUnauthorized: false }
});

interface SearchResult {
  id: number;
  content_id: number;
  similarity_score: number;
  title: string;
  category: string;
  content: string;
}

class DentalNarratorSimulation {
  async simulateFrontendQuery(userQuery: string, carrierName?: string) {
    console.log(`\n🔍 User Query: "${userQuery}"`);
    if (carrierName) {
      console.log(`🏥 Insurance Carrier: ${carrierName}`);
    }
    console.log('=' .repeat(60));
    
    try {
      // Step 1: Generate embedding for user query
      console.log('📊 Step 1: Generating query embedding...');
      const queryEmbedding = await this.generateQueryEmbedding(userQuery);
      console.log('✅ Query embedding generated');
      
      // Step 2: Search for relevant guidelines
      console.log('🔍 Step 2: Searching for relevant guidelines...');
      const relevantGuidelines = await this.searchGuidelines(queryEmbedding, carrierName);
      console.log(`✅ Found ${relevantGuidelines.length} relevant guidelines`);
      
      // Step 3: Generate narrative using guidelines
      console.log('📝 Step 3: Generating dental narrative...');
      const narrative = await this.generateNarrative(userQuery, relevantGuidelines, carrierName);
      console.log('✅ Narrative generated');
      
      // Display results
      console.log('\n🎯 RELEVANT GUIDELINES FOUND:');
      relevantGuidelines.forEach((guideline, index) => {
        console.log(`  ${index + 1}. ${guideline.title} (${(guideline.similarity_score * 100).toFixed(1)}% match)`);
        console.log(`     Category: ${guideline.category}`);
      });
      
      console.log('\n📋 GENERATED DENTAL NARRATIVE:');
      console.log('-' .repeat(60));
      console.log(narrative);
      console.log('-' .repeat(60));
      
      return {
        query: userQuery,
        carrier: carrierName,
        guidelines: relevantGuidelines,
        narrative: narrative
      };
      
    } catch (error) {
      console.error('❌ Error in simulation:', error);
      throw error;
    }
  }

  private async generateQueryEmbedding(query: string): Promise<number[]> {
    const response = await openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: query,
    });
    
    return response.data[0].embedding;
  }

  private async searchGuidelines(queryEmbedding: number[], carrierName?: string): Promise<SearchResult[]> {
    const client = createClient();
    await client.connect();
    
    try {
      // Use our optimized vector search function
      const result = await client.query(`
        SELECT 
          e.id,
          e.content_id,
          (1 - (e.embedding <=> $1::vector)) as similarity_score,
          g.title,
          g.category,
          SUBSTRING(g.content::text, 1, 500) as content_preview
        FROM embeddings e
        JOIN guidelines g ON e.content_id = g.id
        WHERE 
          e.content_type = 'guideline'
          AND (1 - (e.embedding <=> $1::vector)) > 0.7
          AND ($2::text IS NULL OR EXISTS (
            SELECT 1 FROM insurance_carriers ic 
            WHERE ic.id = g.carrier_id 
            AND LOWER(ic.name) LIKE LOWER('%' || $2 || '%')
          ))
        ORDER BY e.embedding <=> $1::vector
        LIMIT 5;
      `, [JSON.stringify(queryEmbedding), carrierName]);
      
      return result.rows.map(row => ({
        id: row.id,
        content_id: row.content_id,
        similarity_score: row.similarity_score,
        title: row.title,
        category: row.category,
        content: row.content_preview
      }));
      
    } finally {
      await client.end();
    }
  }

  private async generateNarrative(userQuery: string, guidelines: SearchResult[], carrierName?: string): Promise<string> {
    const guidelinesContext = guidelines.map(g => 
      `Title: ${g.title}\nCategory: ${g.category}\nContent: ${g.content}`
    ).join('\n\n');
    
    const prompt = `You are a dental narrative generator. Based on the user's query and relevant insurance guidelines, generate a professional dental narrative.

User Query: ${userQuery}
${carrierName ? `Insurance Carrier: ${carrierName}` : ''}

Relevant Guidelines:
${guidelinesContext}

Generate a professional dental narrative that:
1. Addresses the user's specific query
2. Incorporates relevant insurance guidelines
3. Uses proper dental terminology
4. Is suitable for insurance documentation
5. Is concise but comprehensive

Dental Narrative:`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are a professional dental narrative generator with expertise in insurance documentation and dental procedures.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 500,
      temperature: 0.7
    });

    return response.choices[0].message.content || 'Unable to generate narrative.';
  }
}

// Test scenarios
async function runFrontendSimulation() {
  console.log('🚀 Starting Dental Narrator Frontend Simulation...');
  
  const simulator = new DentalNarratorSimulation();
  
  try {
    // Test 1: Crown procedure for Delta Dental
    await simulator.simulateFrontendQuery(
      "Patient needs a crown on tooth #14 due to large cavity. What are the coverage requirements?",
      "Delta Dental"
    );
    
    // Test 2: Root canal procedure for Cigna
    await simulator.simulateFrontendQuery(
      "Root canal therapy required for tooth #19 with periapical infection. Need documentation requirements.",
      "Cigna"
    );
    
    // Test 3: General preventive care
    await simulator.simulateFrontendQuery(
      "Routine cleaning and fluoride treatment for adult patient. What documentation is needed?"
    );
    
    console.log('\n🎉 Frontend simulation completed successfully!');
    
  } catch (error) {
    console.error('💥 Frontend simulation failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  runFrontendSimulation().catch(console.error);
}

export { DentalNarratorSimulation };
