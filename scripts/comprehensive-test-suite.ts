#!/usr/bin/env ts-node

import { DatabaseUtils } from '../src/utils/database';
import { CoreSearchTester } from './test-core-search-functionality';
import { DataEndpointTester } from './test-data-endpoints';

interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
}

interface TestSuite {
  name: string;
  results: TestResult[];
  totalPassed: number;
  totalFailed: number;
  totalDuration: number;
}

class ComprehensiveTestSuite {
  private dbUtils: DatabaseUtils;
  private testSuites: TestSuite[] = [];

  constructor() {
    this.dbUtils = DatabaseUtils.getInstance();
  }

  private async runTest(name: string, testFn: () => Promise<any>): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      return {
        name,
        passed: true,
        duration,
        details: result
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      return {
        name,
        passed: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  async testDatabaseConnectivity(): Promise<TestSuite> {
    console.log('🔌 Testing Database Connectivity...');
    
    const results: TestResult[] = [];
    
    // Test 1: Basic connection
    results.push(await this.runTest('Database Connection', async () => {
      const client = this.dbUtils.createClient();
      await client.connect();
      await client.query('SELECT 1');
      await client.end();
      return 'Connected successfully';
    }));

    // Test 2: Table existence
    results.push(await this.runTest('Table Existence Check', async () => {
      const client = this.dbUtils.createClient();
      await client.connect();
      
      try {
        const tables = ['insurance_carriers', 'procedures', 'guidelines', 'embeddings'];
        const results = [];
        
        for (const table of tables) {
          const result = await client.query(`SELECT COUNT(*) FROM ${table}`);
          results.push({ table, count: parseInt(result.rows[0].count) });
        }
        
        return results;
      } finally {
        await client.end();
      }
    }));

    // Test 3: Vector extension
    results.push(await this.runTest('Vector Extension Check', async () => {
      const client = this.dbUtils.createClient();
      await client.connect();
      
      try {
        await client.query('SELECT 1 FROM embeddings WHERE embedding IS NOT NULL LIMIT 1');
        return 'Vector extension working';
      } finally {
        await client.end();
      }
    }));

    const suite: TestSuite = {
      name: 'Database Connectivity',
      results,
      totalPassed: results.filter(r => r.passed).length,
      totalFailed: results.filter(r => !r.passed).length,
      totalDuration: results.reduce((sum, r) => sum + r.duration, 0)
    };

    this.testSuites.push(suite);
    return suite;
  }

  async testSearchFunctionality(): Promise<TestSuite> {
    console.log('🔍 Testing Search Functionality...');
    
    const results: TestResult[] = [];

    // Test 1: Guidelines search
    results.push(await this.runTest('Guidelines Search', async () => {
      const guidelines = await this.dbUtils.searchGuidelines('crown procedure requirements', { limit: 5 });
      if (guidelines.length === 0) throw new Error('No guidelines found');
      return { count: guidelines.length, sample: guidelines[0]?.title };
    }));

    // Test 2: Procedure lookup
    results.push(await this.runTest('Procedure Lookup', async () => {
      const procedures = await this.dbUtils.lookupProcedure('crown');
      if (procedures.length === 0) throw new Error('No procedures found');
      return { count: procedures.length, sample: procedures[0]?.name };
    }));

    // Test 3: Carrier lookup
    results.push(await this.runTest('Carrier Lookup', async () => {
      const carrier = await this.dbUtils.lookupCarrier('Delta');
      if (!carrier) throw new Error('No carrier found');
      return { name: carrier.name, payer_id: carrier.payer_id };
    }));

    // Test 4: Vector search
    results.push(await this.runTest('Vector Search', async () => {
      const results = await this.dbUtils.vectorSearch('documentation requirements', { limit: 5 });
      if (results.length === 0) throw new Error('No vector results found');
      return { count: results.length, avgSimilarity: results.reduce((sum, r) => sum + r.similarity_score, 0) / results.length };
    }));

    // Test 5: Search with filters
    results.push(await this.runTest('Filtered Search', async () => {
      const deltaResults = await this.dbUtils.searchGuidelines('coverage', { carrier: 'Delta', limit: 3 });
      const categoryResults = await this.dbUtils.searchGuidelines('procedure', { category: 'procedures', limit: 3 });
      return { deltaCount: deltaResults.length, categoryCount: categoryResults.length };
    }));

    const suite: TestSuite = {
      name: 'Search Functionality',
      results,
      totalPassed: results.filter(r => r.passed).length,
      totalFailed: results.filter(r => !r.passed).length,
      totalDuration: results.reduce((sum, r) => sum + r.duration, 0)
    };

    this.testSuites.push(suite);
    return suite;
  }

  async testDataIntegrity(): Promise<TestSuite> {
    console.log('🔍 Testing Data Integrity...');
    
    const results: TestResult[] = [];

    // Test 1: Carrier data quality
    results.push(await this.runTest('Carrier Data Quality', async () => {
      const client = this.dbUtils.createClient();
      await client.connect();
      
      try {
        const totalCarriers = await client.query('SELECT COUNT(*) FROM insurance_carriers');
        const carriersWithPayer = await client.query('SELECT COUNT(*) FROM insurance_carriers WHERE payer_id IS NOT NULL');
        const duplicatePayers = await client.query('SELECT payer_id, COUNT(*) FROM insurance_carriers WHERE payer_id IS NOT NULL GROUP BY payer_id HAVING COUNT(*) > 1');
        
        return {
          total: parseInt(totalCarriers.rows[0].count),
          withPayerId: parseInt(carriersWithPayer.rows[0].count),
          duplicatePayerIds: duplicatePayers.rows.length
        };
      } finally {
        await client.end();
      }
    }));

    // Test 2: Procedure data quality
    results.push(await this.runTest('Procedure Data Quality', async () => {
      const client = this.dbUtils.createClient();
      await client.connect();
      
      try {
        const totalProcedures = await client.query('SELECT COUNT(*) FROM procedures');
        const proceduresWithCategory = await client.query('SELECT COUNT(*) FROM procedures WHERE category IS NOT NULL');
        const duplicateCodes = await client.query('SELECT procedure_code, COUNT(*) FROM procedures GROUP BY procedure_code HAVING COUNT(*) > 1');
        
        return {
          total: parseInt(totalProcedures.rows[0].count),
          withCategory: parseInt(proceduresWithCategory.rows[0].count),
          duplicateCodes: duplicateCodes.rows.length
        };
      } finally {
        await client.end();
      }
    }));

    // Test 3: Guidelines-Embeddings consistency
    results.push(await this.runTest('Guidelines-Embeddings Consistency', async () => {
      const client = this.dbUtils.createClient();
      await client.connect();
      
      try {
        const guidelinesCount = await client.query('SELECT COUNT(*) FROM guidelines');
        const embeddingsCount = await client.query('SELECT COUNT(*) FROM embeddings WHERE content_type = \'guideline\'');
        const orphanedEmbeddings = await client.query(`
          SELECT COUNT(*) FROM embeddings e 
          WHERE e.content_type = 'guideline' 
          AND NOT EXISTS (SELECT 1 FROM guidelines g WHERE g.id = e.content_id)
        `);
        
        return {
          guidelines: parseInt(guidelinesCount.rows[0].count),
          embeddings: parseInt(embeddingsCount.rows[0].count),
          orphaned: parseInt(orphanedEmbeddings.rows[0].count)
        };
      } finally {
        await client.end();
      }
    }));

    // Test 4: Documentation requirements coverage
    results.push(await this.runTest('Documentation Requirements Coverage', async () => {
      const client = this.dbUtils.createClient();
      await client.connect();
      
      try {
        const totalReqs = await client.query('SELECT COUNT(*) FROM documentation_requirements');
        const reqsWithCarrier = await client.query('SELECT COUNT(*) FROM documentation_requirements WHERE carrier_id IS NOT NULL');
        const distinctTypes = await client.query('SELECT COUNT(DISTINCT document_type) FROM documentation_requirements');
        
        return {
          total: parseInt(totalReqs.rows[0].count),
          withCarrier: parseInt(reqsWithCarrier.rows[0].count),
          distinctTypes: parseInt(distinctTypes.rows[0].count)
        };
      } finally {
        await client.end();
      }
    }));

    const suite: TestSuite = {
      name: 'Data Integrity',
      results,
      totalPassed: results.filter(r => r.passed).length,
      totalFailed: results.filter(r => !r.passed).length,
      totalDuration: results.reduce((sum, r) => sum + r.duration, 0)
    };

    this.testSuites.push(suite);
    return suite;
  }

  async testPerformance(): Promise<TestSuite> {
    console.log('⚡ Testing Performance...');
    
    const results: TestResult[] = [];

    // Test 1: Search performance
    results.push(await this.runTest('Search Performance', async () => {
      const iterations = 5;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const start = Date.now();
        await this.dbUtils.searchGuidelines('crown requirements', { limit: 10 });
        times.push(Date.now() - start);
      }
      
      const avgTime = times.reduce((sum, t) => sum + t, 0) / times.length;
      if (avgTime > 3000) throw new Error(`Search too slow: ${avgTime}ms average`);
      
      return { averageMs: avgTime, iterations, times };
    }));

    // Test 2: Vector search performance
    results.push(await this.runTest('Vector Search Performance', async () => {
      const iterations = 3;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const start = Date.now();
        await this.dbUtils.vectorSearch('documentation requirements', { limit: 10 });
        times.push(Date.now() - start);
      }
      
      const avgTime = times.reduce((sum, t) => sum + t, 0) / times.length;
      if (avgTime > 5000) throw new Error(`Vector search too slow: ${avgTime}ms average`);
      
      return { averageMs: avgTime, iterations, times };
    }));

    // Test 3: Database query performance
    results.push(await this.runTest('Database Query Performance', async () => {
      const client = this.dbUtils.createClient();
      await client.connect();
      
      try {
        const start = Date.now();
        await client.query('SELECT * FROM insurance_carriers LIMIT 100');
        const queryTime = Date.now() - start;
        
        if (queryTime > 1000) throw new Error(`Query too slow: ${queryTime}ms`);
        
        return { queryTimeMs: queryTime };
      } finally {
        await client.end();
      }
    }));

    const suite: TestSuite = {
      name: 'Performance',
      results,
      totalPassed: results.filter(r => r.passed).length,
      totalFailed: results.filter(r => !r.passed).length,
      totalDuration: results.reduce((sum, r) => sum + r.duration, 0)
    };

    this.testSuites.push(suite);
    return suite;
  }

  private printTestSuite(suite: TestSuite): void {
    console.log(`\n📊 ${suite.name} Results:`);
    console.log(`   ✅ Passed: ${suite.totalPassed}`);
    console.log(`   ❌ Failed: ${suite.totalFailed}`);
    console.log(`   ⏱️  Total Time: ${suite.totalDuration}ms`);
    
    if (suite.totalFailed > 0) {
      console.log('\n   Failed Tests:');
      suite.results.filter(r => !r.passed).forEach(result => {
        console.log(`     ❌ ${result.name}: ${result.error}`);
      });
    }
  }

  private printSummary(): void {
    const totalTests = this.testSuites.reduce((sum, suite) => sum + suite.results.length, 0);
    const totalPassed = this.testSuites.reduce((sum, suite) => sum + suite.totalPassed, 0);
    const totalFailed = this.testSuites.reduce((sum, suite) => sum + suite.totalFailed, 0);
    const totalDuration = this.testSuites.reduce((sum, suite) => sum + suite.totalDuration, 0);

    console.log('\n🎯 COMPREHENSIVE TEST SUMMARY');
    console.log('================================');
    console.log(`📊 Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${totalPassed}`);
    console.log(`❌ Failed: ${totalFailed}`);
    console.log(`📈 Success Rate: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);
    console.log(`⏱️  Total Duration: ${totalDuration}ms`);

    if (totalFailed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! System is ready for production.');
    } else {
      console.log('\n⚠️  Some tests failed. Please review the issues above.');
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 COMPREHENSIVE DENTAL NARRATOR TEST SUITE');
    console.log('===========================================\n');

    try {
      await this.testDatabaseConnectivity();
      await this.testSearchFunctionality();
      await this.testDataIntegrity();
      await this.testPerformance();

      // Print individual suite results
      this.testSuites.forEach(suite => this.printTestSuite(suite));

      // Print overall summary
      this.printSummary();

    } catch (error) {
      console.error('❌ Test suite execution failed:', error);
    }
  }
}

// Main execution
async function main() {
  const testSuite = new ComprehensiveTestSuite();
  await testSuite.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { ComprehensiveTestSuite };
