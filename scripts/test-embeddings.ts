import { Client } from 'pg';
import OpenAI from 'openai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Initialize clients
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const createClient = () => new Client({
  connectionString: process.env.POSTGRES_CONNECTION_STRING,
  ssl: { rejectUnauthorized: false }
});

class EmbeddingTester {
  
  async testEmbeddingGeneration() {
    console.log('🧪 Testing OpenAI embedding generation...');
    
    try {
      // Step 1: Test OpenAI API connection
      await this.testOpenAIConnection();
      
      // Step 2: Test database connection
      await this.testDatabaseConnection();
      
      // Step 3: Get a sample guideline
      const sampleGuideline = await this.getSampleGuideline();
      
      // Step 4: Generate embedding for sample
      await this.testSampleEmbedding(sampleGuideline);
      
      // Step 5: Test vector storage and retrieval
      await this.testVectorStorage();
      
      console.log('✅ All embedding tests passed! Ready for full generation.');
      
    } catch (error) {
      console.error('❌ Embedding test failed:', error);
      throw error;
    }
  }

  private async testOpenAIConnection() {
    console.log('🔌 Testing OpenAI API connection...');
    
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('❌ OPENAI_API_KEY not found in environment variables');
    }
    
    try {
      const response = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: 'This is a test sentence for embedding generation.',
      });
      
      console.log(`✅ OpenAI API connected successfully`);
      console.log(`   Model: text-embedding-3-small`);
      console.log(`   Embedding dimensions: ${response.data[0].embedding.length}`);
      console.log(`   Usage tokens: ${response.usage.total_tokens}`);
      
    } catch (error: any) {
      throw new Error(`❌ OpenAI API connection failed: ${error.message}`);
    }
  }

  private async testDatabaseConnection() {
    console.log('🗄️  Testing database connection...');
    
    const client = createClient();
    
    try {
      await client.connect();
      
      // Test basic query
      const result = await client.query('SELECT COUNT(*) as count FROM guidelines');
      const guidelineCount = result.rows[0].count;
      
      console.log(`✅ Database connected successfully`);
      console.log(`   Total guidelines: ${guidelineCount}`);
      
      // Check embeddings table
      const embeddingResult = await client.query('SELECT COUNT(*) as count FROM embeddings');
      const embeddingCount = embeddingResult.rows[0].count;
      
      console.log(`   Existing embeddings: ${embeddingCount}`);
      
      // Check if pgvector extension is available
      try {
        await client.query("SELECT '1'::vector");
        console.log('✅ pgvector extension is available');
      } catch (error) {
        console.warn('⚠️  pgvector extension may not be properly configured');
      }
      
    } finally {
      await client.end();
    }
  }

  private async getSampleGuideline() {
    console.log('📋 Getting sample guideline...');
    
    const client = createClient();
    await client.connect();
    
    try {
      const query = `
        SELECT id, carrier_id, category, title, content
        FROM guidelines 
        WHERE content IS NOT NULL 
        LIMIT 1
      `;
      
      const result = await client.query(query);
      
      if (result.rows.length === 0) {
        throw new Error('No guidelines found in database');
      }
      
      const guideline = result.rows[0];
      console.log(`✅ Sample guideline retrieved: ID ${guideline.id}`);
      console.log(`   Title: ${guideline.title}`);
      console.log(`   Category: ${guideline.category}`);
      
      return guideline;
      
    } finally {
      await client.end();
    }
  }

  private async testSampleEmbedding(guideline: any) {
    console.log('🔢 Testing embedding generation for sample guideline...');
    
    // Extract text content
    let textContent = '';
    try {
      if (typeof guideline.content === 'string') {
        textContent = guideline.content;
      } else if (typeof guideline.content === 'object' && guideline.content.content) {
        textContent = guideline.content.content;
      } else {
        textContent = JSON.stringify(guideline.content);
      }
    } catch (error) {
      textContent = guideline.title;
    }
    
    console.log(`   Text length: ${textContent.length} characters`);
    console.log(`   Preview: ${textContent.substring(0, 100)}...`);
    
    // Generate embedding
    const startTime = Date.now();
    
    try {
      const response = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: textContent,
      });
      
      const embedding = response.data[0].embedding;
      const duration = Date.now() - startTime;
      
      console.log(`✅ Embedding generated successfully`);
      console.log(`   Dimensions: ${embedding.length}`);
      console.log(`   Generation time: ${duration}ms`);
      console.log(`   Tokens used: ${response.usage.total_tokens}`);
      
      return {
        guideline_id: guideline.id,
        embedding: embedding,
        text_content: textContent,
        metadata: {
          carrier_id: guideline.carrier_id,
          category: guideline.category,
          title: guideline.title,
          text_length: textContent.length,
          generated_at: new Date().toISOString()
        }
      };
      
    } catch (error: any) {
      throw new Error(`❌ Embedding generation failed: ${error.message}`);
    }
  }

  private async testVectorStorage() {
    console.log('💾 Testing vector storage and retrieval...');
    
    const client = createClient();
    await client.connect();
    
    try {
      // Create a test embedding
      const testEmbedding = Array.from({ length: 1536 }, () => Math.random());
      const testMetadata = {
        test: true,
        created_at: new Date().toISOString()
      };
      
      // Insert test embedding
      const insertQuery = `
        INSERT INTO embeddings (content_type, content_id, embedding, metadata)
        VALUES ($1, $2, $3::vector, $4::jsonb)
        RETURNING id
      `;
      
      const insertResult = await client.query(insertQuery, [
        'test',
        999999,
        JSON.stringify(testEmbedding),
        JSON.stringify(testMetadata)
      ]);
      
      const embeddingId = insertResult.rows[0].id;
      console.log(`✅ Test embedding stored with ID: ${embeddingId}`);
      
      // Retrieve and verify
      const selectQuery = `
        SELECT content_type, content_id, metadata, embedding
        FROM embeddings
        WHERE id = $1
      `;
      
      const selectResult = await client.query(selectQuery, [embeddingId]);
      const retrieved = selectResult.rows[0];
      
      console.log('✅ Test embedding retrieved successfully');
      console.log(`   Content type: ${retrieved.content_type}`);
      console.log(`   Content ID: ${retrieved.content_id}`);
      console.log(`   Embedding exists: ${retrieved.embedding ? 'Yes' : 'No'}`);
      console.log(`   Metadata: ${JSON.stringify(retrieved.metadata)}`);
      
      // Clean up test data
      await client.query('DELETE FROM embeddings WHERE id = $1', [embeddingId]);
      console.log('✅ Test data cleaned up');
      
    } finally {
      await client.end();
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting embedding generation test...');
  
  const tester = new EmbeddingTester();
  
  try {
    await tester.testEmbeddingGeneration();
    console.log('\n🎉 All tests passed! The embedding system is ready.');
    console.log('\n📋 Next steps:');
    console.log('1. Ensure OPENAI_API_KEY is set in .env.development');
    console.log('2. Run: npm run generate-embeddings');
    console.log('3. Monitor progress and wait for completion');
    
  } catch (error) {
    console.error('\n💥 Test failed:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check OPENAI_API_KEY in .env.development');
    console.log('2. Verify database connection string');
    console.log('3. Ensure pgvector extension is installed');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { EmbeddingTester };
