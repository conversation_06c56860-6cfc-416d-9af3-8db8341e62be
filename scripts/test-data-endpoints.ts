#!/usr/bin/env ts-node

import { DatabaseUtils } from '../src/utils/database';

class DataEndpointTester {
  private dbUtils: DatabaseUtils;

  constructor() {
    this.dbUtils = DatabaseUtils.getInstance();
  }

  async testCarriersRetrieval(): Promise<void> {
    console.log('🏥 Testing Carriers Retrieval...');
    
    try {
      const client = this.dbUtils.createClient();
      await client.connect();

      try {
        // Test basic carriers query
        const result = await client.query(`
          SELECT 
            id,
            name,
            code,
            website,
            contact_info,
            created_at,
            updated_at
          FROM insurance_carriers
          ORDER BY name ASC 
          LIMIT 5
        `);

        console.log(`✅ Found ${result.rows.length} carriers`);
        
        if (result.rows.length > 0) {
          console.log('📋 Sample carriers:');
          result.rows.forEach((carrier, index) => {
            console.log(`  ${index + 1}. ${carrier.name} (${carrier.code})`);
            console.log(`     Website: ${carrier.website || 'N/A'}`);
          });
        }

        // Test search functionality
        const searchResult = await client.query(`
          SELECT COUNT(*) as total 
          FROM insurance_carriers 
          WHERE (name ILIKE $1 OR code ILIKE $1)
        `, ['%Delta%']);

        console.log(`✅ Delta search results: ${searchResult.rows[0].total}`);

      } finally {
        await client.end();
      }

    } catch (error) {
      console.error('❌ Carriers retrieval failed:', error);
    }
  }

  async testProceduresRetrieval(): Promise<void> {
    console.log('\n🦷 Testing Procedures Retrieval...');
    
    try {
      const client = this.dbUtils.createClient();
      await client.connect();

      try {
        // Test basic procedures query
        const result = await client.query(`
          SELECT 
            id,
            cdt_code,
            name,
            description,
            category,
            created_at,
            updated_at
          FROM procedures
          ORDER BY cdt_code ASC 
          LIMIT 5
        `);

        console.log(`✅ Found ${result.rows.length} procedures`);
        
        if (result.rows.length > 0) {
          console.log('📋 Sample procedures:');
          result.rows.forEach((procedure, index) => {
            console.log(`  ${index + 1}. ${procedure.cdt_code} - ${procedure.name}`);
            console.log(`     Category: ${procedure.category}`);
          });
        }

        // Test category filtering
        const categoryResult = await client.query(`
          SELECT COUNT(*) as total 
          FROM procedures 
          WHERE category ILIKE $1
        `, ['%restorative%']);

        console.log(`✅ Restorative procedures: ${categoryResult.rows[0].total}`);

        // Test search functionality
        const searchResult = await client.query(`
          SELECT COUNT(*) as total 
          FROM procedures 
          WHERE (cdt_code ILIKE $1 OR name ILIKE $1 OR description ILIKE $1)
        `, ['%crown%']);

        console.log(`✅ Crown search results: ${searchResult.rows[0].total}`);

      } finally {
        await client.end();
      }

    } catch (error) {
      console.error('❌ Procedures retrieval failed:', error);
    }
  }

  async testAppealsRetrieval(): Promise<void> {
    console.log('\n⚖️ Testing Appeals Retrieval...');
    
    try {
      const client = this.dbUtils.createClient();
      await client.connect();

      try {
        // Test appeals query
        const result = await client.query(`
          SELECT 
            ap.id,
            ap.procedure_type,
            ap.steps,
            ap.timeframes,
            ap.contact_info,
            ap.required_forms,
            ic.name as carrier_name,
            ap.created_at,
            ap.updated_at
          FROM appeal_procedures ap
          LEFT JOIN insurance_carriers ic ON ap.carrier_id = ic.id
          ORDER BY ic.name ASC, ap.procedure_type ASC 
          LIMIT 5
        `);

        console.log(`✅ Found ${result.rows.length} appeal procedures`);
        
        if (result.rows.length > 0) {
          console.log('📋 Sample appeals:');
          result.rows.forEach((appeal, index) => {
            console.log(`  ${index + 1}. ${appeal.carrier_name || 'Unknown'} - ${appeal.procedure_type}`);
            if (appeal.timeframes) {
              console.log(`     Timeframes: ${JSON.stringify(appeal.timeframes)}`);
            }
          });
        } else {
          console.log('ℹ️  No appeal procedures found - this is expected if appeals data hasn\'t been populated yet');
        }

      } finally {
        await client.end();
      }

    } catch (error) {
      console.error('❌ Appeals retrieval failed:', error);
    }
  }

  async testPaginationLogic(): Promise<void> {
    console.log('\n📄 Testing Pagination Logic...');
    
    try {
      const client = this.dbUtils.createClient();
      await client.connect();

      try {
        // Test total count
        const countResult = await client.query('SELECT COUNT(*) as total FROM insurance_carriers');
        const totalCarriers = parseInt(countResult.rows[0].total);
        console.log(`✅ Total carriers: ${totalCarriers}`);

        // Test pagination
        const limit = 10;
        const offset = 0;
        
        const paginatedResult = await client.query(`
          SELECT name, code 
          FROM insurance_carriers 
          ORDER BY name ASC 
          LIMIT $1 OFFSET $2
        `, [limit, offset]);

        console.log(`✅ Paginated results (limit ${limit}, offset ${offset}): ${paginatedResult.rows.length}`);
        
        const hasMore = offset + limit < totalCarriers;
        console.log(`✅ Has more pages: ${hasMore}`);

        // Test procedures count
        const procedureCountResult = await client.query('SELECT COUNT(*) as total FROM procedures');
        const totalProcedures = parseInt(procedureCountResult.rows[0].total);
        console.log(`✅ Total procedures: ${totalProcedures}`);

      } finally {
        await client.end();
      }

    } catch (error) {
      console.error('❌ Pagination test failed:', error);
    }
  }

  async testDataQuality(): Promise<void> {
    console.log('\n🔍 Testing Data Quality...');
    
    try {
      const client = this.dbUtils.createClient();
      await client.connect();

      try {
        // Check for carriers with missing data
        const carriersWithoutWebsite = await client.query(`
          SELECT COUNT(*) as count 
          FROM insurance_carriers 
          WHERE website IS NULL OR website = ''
        `);
        console.log(`ℹ️  Carriers without website: ${carriersWithoutWebsite.rows[0].count}`);

        // Check for procedures without categories
        const proceduresWithoutCategory = await client.query(`
          SELECT COUNT(*) as count 
          FROM procedures 
          WHERE category IS NULL OR category = ''
        `);
        console.log(`ℹ️  Procedures without category: ${proceduresWithoutCategory.rows[0].count}`);

        // Check for unique CDT codes
        const duplicateCDTCodes = await client.query(`
          SELECT cdt_code, COUNT(*) as count 
          FROM procedures 
          GROUP BY cdt_code 
          HAVING COUNT(*) > 1
        `);
        console.log(`ℹ️  Duplicate CDT codes: ${duplicateCDTCodes.rows.length}`);

        // Check carrier code uniqueness
        const duplicateCarrierCodes = await client.query(`
          SELECT code, COUNT(*) as count 
          FROM insurance_carriers 
          GROUP BY code 
          HAVING COUNT(*) > 1
        `);
        console.log(`ℹ️  Duplicate carrier codes: ${duplicateCarrierCodes.rows.length}`);

      } finally {
        await client.end();
      }

    } catch (error) {
      console.error('❌ Data quality test failed:', error);
    }
  }

  async runPerformanceTest(): Promise<void> {
    console.log('\n⚡ Performance Testing Data Endpoints...');
    
    const testCases = [
      {
        name: 'Carriers Query',
        fn: async () => {
          const client = this.dbUtils.createClient();
          await client.connect();
          try {
            await client.query('SELECT * FROM insurance_carriers LIMIT 20');
          } finally {
            await client.end();
          }
        }
      },
      {
        name: 'Procedures Query',
        fn: async () => {
          const client = this.dbUtils.createClient();
          await client.connect();
          try {
            await client.query('SELECT * FROM procedures LIMIT 20');
          } finally {
            await client.end();
          }
        }
      },
      {
        name: 'Carrier Search',
        fn: async () => {
          const client = this.dbUtils.createClient();
          await client.connect();
          try {
            await client.query('SELECT * FROM insurance_carriers WHERE name ILIKE $1 LIMIT 10', ['%Delta%']);
          } finally {
            await client.end();
          }
        }
      }
    ];

    for (const testCase of testCases) {
      const startTime = Date.now();
      
      try {
        await testCase.fn();
        const duration = Date.now() - startTime;
        console.log(`⏱️  ${testCase.name}: ${duration}ms`);
      } catch (error) {
        console.log(`❌ ${testCase.name}: Failed`);
      }
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🧪 Testing Data Retrieval Endpoints');
    console.log('===================================\n');

    await this.testCarriersRetrieval();
    await this.testProceduresRetrieval();
    await this.testAppealsRetrieval();
    await this.testPaginationLogic();
    await this.testDataQuality();
    await this.runPerformanceTest();

    console.log('\n🎉 Data retrieval endpoints testing completed!');
  }
}

// Main execution
async function main() {
  const tester = new DataEndpointTester();
  await tester.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { DataEndpointTester };
