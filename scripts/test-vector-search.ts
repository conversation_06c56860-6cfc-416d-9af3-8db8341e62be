import { Client } from 'pg';
import OpenAI from 'openai';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Initialize clients
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const createClient = () => new Client({
  connectionString: process.env.POSTGRES_CONNECTION_STRING,
  ssl: { rejectUnauthorized: false }
});

interface SearchResult {
  id: number;
  content_id: number;
  similarity_score: number;
  title: string;
  category: string;
  hybrid_score?: number;
  recency_score?: number;
}

class VectorSearchTester {
  async testVectorSearch() {
    console.log('🧪 Testing vector search functionality...');
    
    try {
      // Test 1: Basic vector search performance
      await this.testBasicVectorSearch();
      
      // Test 2: Hybrid search functionality
      await this.testHybridSearch();
      
      // Test 3: Filtered search by category
      await this.testFilteredSearch();
      
      // Test 4: Performance benchmarking
      await this.benchmarkPerformance();
      
      console.log('🎉 All vector search tests completed successfully!');
      
    } catch (error) {
      console.error('❌ Vector search testing failed:', error);
      throw error;
    }
  }

  private async testBasicVectorSearch() {
    console.log('\n📝 Test 1: Basic Vector Search');
    console.log('=' .repeat(40));
    
    const client = createClient();
    await client.connect();
    
    try {
      // Generate a test query embedding
      const testQuery = "dental crown procedure requirements and documentation";
      console.log(`🔍 Query: "${testQuery}"`);
      
      const embedding = await this.generateQueryEmbedding(testQuery);
      
      // Test the vector search function
      const startTime = Date.now();
      const result = await client.query(`
        SELECT 
          id,
          content_id,
          similarity_score,
          metadata->>'title' as title,
          metadata->>'category' as category
        FROM search_guidelines_vector($1::vector, 'guideline', NULL, NULL, 0.7, 5)
        ORDER BY similarity_score DESC;
      `, [JSON.stringify(embedding)]);
      
      const endTime = Date.now();
      const searchTime = endTime - startTime;
      
      console.log(`⚡ Search completed in ${searchTime}ms`);
      console.log(`📊 Found ${result.rows.length} similar guidelines`);
      
      if (result.rows.length > 0) {
        console.log('🎯 Top results:');
        result.rows.forEach((row: SearchResult, index: number) => {
          console.log(`  ${index + 1}. ${row.title} (${(row.similarity_score * 100).toFixed(1)}% similarity)`);
        });
      }
      
      // Performance check
      if (searchTime < 500) {
        console.log('✅ Performance target achieved (<500ms)');
      } else {
        console.log('⚠️  Performance target not met (>500ms)');
      }
      
    } finally {
      await client.end();
    }
  }

  private async testHybridSearch() {
    console.log('\n🔄 Test 2: Hybrid Search (Similarity + Recency)');
    console.log('=' .repeat(40));
    
    const client = createClient();
    await client.connect();
    
    try {
      const testQuery = "insurance claim appeal process";
      console.log(`🔍 Query: "${testQuery}"`);
      
      const embedding = await this.generateQueryEmbedding(testQuery);
      
      const startTime = Date.now();
      const result = await client.query(`
        SELECT 
          id,
          content_id,
          similarity_score,
          recency_score,
          hybrid_score,
          metadata->>'title' as title,
          metadata->>'category' as category
        FROM search_guidelines_hybrid($1::vector, NULL, NULL, 0.6, 5)
        ORDER BY hybrid_score DESC;
      `, [JSON.stringify(embedding)]);
      
      const endTime = Date.now();
      const searchTime = endTime - startTime;
      
      console.log(`⚡ Hybrid search completed in ${searchTime}ms`);
      console.log(`📊 Found ${result.rows.length} results`);
      
      if (result.rows.length > 0) {
        console.log('🎯 Top hybrid results:');
        result.rows.forEach((row: any, index: number) => {
          console.log(`  ${index + 1}. ${row.title}`);
          console.log(`     Similarity: ${(row.similarity_score * 100).toFixed(1)}%, Recency: ${(row.recency_score * 100).toFixed(1)}%, Hybrid: ${(row.hybrid_score * 100).toFixed(1)}%`);
        });
      }
      
    } finally {
      await client.end();
    }
  }

  private async testFilteredSearch() {
    console.log('\n🎛️  Test 3: Filtered Search by Category');
    console.log('=' .repeat(40));
    
    const client = createClient();
    await client.connect();
    
    try {
      const testQuery = "preventive care guidelines";
      console.log(`🔍 Query: "${testQuery}"`);
      
      const embedding = await this.generateQueryEmbedding(testQuery);
      
      // Test search with category filter
      const result = await client.query(`
        SELECT 
          id,
          content_id,
          similarity_score,
          metadata->>'title' as title,
          metadata->>'category' as category
        FROM search_guidelines_vector($1::vector, 'guideline', NULL, 'procedures', 0.5, 5)
        ORDER BY similarity_score DESC;
      `, [JSON.stringify(embedding)]);
      
      console.log(`📊 Found ${result.rows.length} results in 'procedures' category`);
      
      if (result.rows.length > 0) {
        console.log('🎯 Filtered results:');
        result.rows.forEach((row: SearchResult, index: number) => {
          console.log(`  ${index + 1}. ${row.title} (${row.category})`);
        });
      }
      
    } finally {
      await client.end();
    }
  }

  private async benchmarkPerformance() {
    console.log('\n⚡ Test 4: Performance Benchmarking');
    console.log('=' .repeat(40));
    
    const client = createClient();
    await client.connect();
    
    try {
      const testQueries = [
        "dental crown procedure",
        "root canal treatment",
        "orthodontic coverage",
        "preventive care benefits",
        "claim submission requirements"
      ];
      
      const times: number[] = [];
      
      for (const query of testQueries) {
        const embedding = await this.generateQueryEmbedding(query);
        
        const startTime = Date.now();
        await client.query(`
          SELECT COUNT(*) 
          FROM search_guidelines_vector($1::vector, 'guideline', NULL, NULL, 0.7, 10);
        `, [JSON.stringify(embedding)]);
        const endTime = Date.now();
        
        const searchTime = endTime - startTime;
        times.push(searchTime);
        console.log(`  "${query}": ${searchTime}ms`);
      }
      
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      
      console.log('\n📈 Performance Summary:');
      console.log(`  Average: ${avgTime.toFixed(1)}ms`);
      console.log(`  Min: ${minTime}ms`);
      console.log(`  Max: ${maxTime}ms`);
      
      if (avgTime < 500) {
        console.log('✅ Average performance target achieved');
      } else {
        console.log('⚠️  Average performance target not met');
      }
      
    } finally {
      await client.end();
    }
  }

  private async generateQueryEmbedding(query: string): Promise<number[]> {
    try {
      const response = await openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: query,
      });
      
      return response.data[0].embedding;
      
    } catch (error) {
      console.error('❌ Failed to generate query embedding:', error);
      throw error;
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting vector search testing...');
  
  const tester = new VectorSearchTester();
  
  try {
    await tester.testVectorSearch();
    console.log('\n🎉 Vector search testing completed successfully!');
  } catch (error) {
    console.error('💥 Vector search testing failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { VectorSearchTester };
