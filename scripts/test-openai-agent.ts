import { runDentalNarrator } from '../src/agents/dental-narrator-agent';

async function testOpenAIAgent() {
  console.log('🧪 Testing OpenAI Agents SDK Implementation...');
  console.log('=' .repeat(60));
  
  try {
    // Test 1: Crown procedure for Delta Dental
    console.log('\n📝 Test 1: Crown Procedure for Delta Dental');
    console.log('-' .repeat(50));
    
    const testQuery1 = `Generate a professional dental narrative for these chart notes for Delta Dental:

Patient: <PERSON>, DOB: 01/15/1980
Chief Complaint: "My tooth hurts when I bite down"
Clinical Findings: Tooth #14 (upper left first molar) has large amalgam restoration with recurrent decay. Radiographs show decay extending close to pulp chamber. Percussion test negative. Cold test shows delayed response.
Treatment: Crown preparation completed, temporary crown placed
Procedure Code: D2750 - Crown - porcelain fused to high noble metal
Next Appointment: Crown delivery in 2 weeks`;

    const response1 = await runDentalNarrator(testQuery1);
    console.log('🤖 Agent Response:');
    console.log(response1);
    
    // Test 2: Root canal procedure for Cigna
    console.log('\n\n📝 Test 2: Root Canal Procedure for Cigna');
    console.log('-' .repeat(50));
    
    const testQuery2 = `Generate a professional dental narrative for these chart notes for Cigna:

Patient: <PERSON>, DO<PERSON>: 03/22/1975
Chief Complaint: "Severe tooth pain keeping me awake at night"
Clinical Findings: Tooth #19 (lower left first molar) with deep caries, positive percussion test, and periapical radiolucency on radiograph. Pulp vitality test negative.
Treatment: Root canal therapy completed in single visit with post and core placement
Procedure Codes: D3330 - Molar endodontic therapy, D2954 - Prefabricated post and core`;

    const response2 = await runDentalNarrator(testQuery2);
    console.log('🤖 Agent Response:');
    console.log(response2);
    
    // Test 3: General inquiry about preventive care
    console.log('\n\n📝 Test 3: Preventive Care Guidelines');
    console.log('-' .repeat(50));
    
    const testQuery3 = `What are the documentation requirements for preventive care procedures like cleanings and fluoride treatments? I need to know what information to include in my narratives.`;

    const response3 = await runDentalNarrator(testQuery3);
    console.log('🤖 Agent Response:');
    console.log(response3);
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Summary:');
    console.log('✅ OpenAI Agents SDK integration working');
    console.log('✅ Vector search tools functioning');
    console.log('✅ Database connectivity established');
    console.log('✅ Professional narrative generation active');
    
  } catch (error) {
    console.error('❌ Error testing OpenAI agent:', error);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting OpenAI Agents SDK Test...');
  
  try {
    await testOpenAIAgent();
  } catch (error) {
    console.error('💥 OpenAI agent test failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { testOpenAIAgent };
