#!/usr/bin/env ts-node

import { config } from 'dotenv';
import { Pool } from 'pg';
import { dbUtils } from '../src/utils/database';

// Load environment variables
config({ path: '.env.development' });

// Database connection
const dbUrl = process.env.POSTGRES_CONNECTION_STRING || 'postgresql://postgres:postgres@127.0.0.1:54322/postgres';
const pool = new Pool({ connectionString: dbUrl });

/**
 * Vector Performance Optimizer
 * Ensures optimal vector search performance for OpenAI Agents SDK
 */
class VectorPerformanceOptimizer {
  
  async optimizeVectorIndexes() {
    console.log('🚀 Optimizing vector indexes for OpenAI Agents SDK...');
    
    try {
      // Step 1: Check current vector indexes
      await this.checkVectorIndexes();
      
      // Step 2: Analyze vector search performance
      await this.analyzeSearchPerformance();
      
      // Step 3: Update table statistics
      await this.updateTableStatistics();
      
      // Step 4: Test vector search performance
      await this.testVectorSearchPerformance();
      
      console.log('✅ Vector optimization completed successfully!');
      
    } catch (error) {
      console.error('❌ Error optimizing vector indexes:', error);
      throw error;
    }
  }

  private async checkVectorIndexes() {
    console.log('📊 Checking vector indexes...');
    
    const client = await pool.connect();
    
    try {
      // Check for vector indexes
      const indexQuery = `
        SELECT 
          indexname, 
          indexdef,
          pg_size_pretty(pg_relation_size(indexname::regclass)) as size
        FROM pg_indexes 
        WHERE tablename = 'embeddings' 
          AND (indexdef ILIKE '%ivfflat%' OR indexdef ILIKE '%hnsw%')
      `;
      
      const result = await client.query(indexQuery);
      
      if (result.rows.length === 0) {
        console.log('⚠️  No vector indexes found. Creating cosine similarity index...');
        
        // Create vector index for cosine similarity
        await client.query(`
          CREATE INDEX IF NOT EXISTS idx_embeddings_vector_cosine 
          ON embeddings USING ivfflat (embedding vector_cosine_ops) 
          WITH (lists = 50)
        `);
        
        console.log('✅ Created cosine similarity vector index');
      } else {
        console.log('✅ Found vector indexes:');
        result.rows.forEach(row => {
          console.log(`  - ${row.indexname}: ${row.size}`);
        });
      }
      
    } finally {
      client.release();
    }
  }

  private async analyzeSearchPerformance() {
    console.log('🔍 Analyzing search performance...');
    
    const client = await pool.connect();
    
    try {
      // Get embeddings table statistics
      const statsQuery = `
        SELECT 
          COUNT(*) as total_embeddings,
          COUNT(DISTINCT content_type) as content_types,
          COUNT(DISTINCT (metadata->>'carrier_id')) as unique_carriers,
          COUNT(DISTINCT (metadata->>'category')) as unique_categories,
          pg_size_pretty(pg_total_relation_size('embeddings')) as table_size
        FROM embeddings
      `;
      
      const result = await client.query(statsQuery);
      const stats = result.rows[0];
      
      console.log('📈 Embeddings table statistics:');
      console.log(`  - Total embeddings: ${stats.total_embeddings}`);
      console.log(`  - Content types: ${stats.content_types}`);
      console.log(`  - Unique carriers: ${stats.unique_carriers}`);
      console.log(`  - Unique categories: ${stats.unique_categories}`);
      console.log(`  - Table size: ${stats.table_size}`);
      
      // Check for missing embeddings
      const missingQuery = `
        SELECT 
          COUNT(*) as guidelines_without_embeddings
        FROM guidelines g
        LEFT JOIN embeddings e ON e.content_type = 'guideline' AND e.content_id = g.id
        WHERE e.id IS NULL
      `;
      
      const missingResult = await client.query(missingQuery);
      const missingCount = missingResult.rows[0].guidelines_without_embeddings;
      
      if (missingCount > 0) {
        console.log(`⚠️  Found ${missingCount} guidelines without embeddings`);
        console.log('   Run: npm run generate-embeddings');
      } else {
        console.log('✅ All guidelines have embeddings');
      }
      
    } finally {
      client.release();
    }
  }

  private async updateTableStatistics() {
    console.log('📊 Updating table statistics...');
    
    const client = await pool.connect();
    
    try {
      // Update table statistics for better query planning
      await client.query('ANALYZE embeddings');
      await client.query('ANALYZE guidelines');
      await client.query('ANALYZE insurance_carriers');
      
      console.log('✅ Table statistics updated');
      
    } finally {
      client.release();
    }
  }

  private async testVectorSearchPerformance() {
    console.log('⚡ Testing vector search performance...');
    
    const testQueries = [
      'dental crown procedure requirements',
      'root canal therapy documentation',
      'preventive care coverage guidelines',
      'orthodontic treatment limitations',
      'periodontal surgery requirements'
    ];
    
    const performanceResults = [];
    
    for (const query of testQueries) {
      const startTime = Date.now();
      
      try {
        const results = await dbUtils.searchGuidelines(query, { limit: 5 });
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        performanceResults.push({
          query: query.substring(0, 30) + '...',
          duration,
          results: results.length
        });
        
        console.log(`  ✓ "${query.substring(0, 30)}...": ${duration}ms (${results.length} results)`);
        
      } catch (error) {
        console.log(`  ✗ "${query.substring(0, 30)}...": Error - ${error instanceof Error ? error.message : String(error)}`);
      }
    }
    
    // Calculate average performance
    const avgDuration = performanceResults.reduce((sum, result) => sum + result.duration, 0) / performanceResults.length;
    const totalResults = performanceResults.reduce((sum, result) => sum + result.results, 0);
    
    console.log('\n📊 Performance Summary:');
    console.log(`  - Average query time: ${avgDuration.toFixed(1)}ms`);
    console.log(`  - Total results retrieved: ${totalResults}`);
    console.log(`  - Queries tested: ${performanceResults.length}`);
    
    if (avgDuration < 100) {
      console.log('🚀 Excellent performance! Vector search is optimized.');
    } else if (avgDuration < 500) {
      console.log('✅ Good performance. Vector search is working well.');
    } else {
      console.log('⚠️  Performance could be improved. Consider optimizing indexes.');
    }
  }

  async checkDatabaseHealth() {
    console.log('🏥 Checking database health...');
    
    try {
      const stats = await dbUtils.getDatabaseStats();
      
      console.log('📊 Database Health Report:');
      console.log(`  - Total guidelines: ${stats.totalGuidelines}`);
      console.log(`  - Total embeddings: ${stats.totalEmbeddings}`);
      console.log(`  - Total carriers: ${stats.totalCarriers}`);
      console.log(`  - Vector extension: ${stats.vectorExtensionEnabled ? '✅ Enabled' : '❌ Disabled'}`);
      
      // Check data consistency
      const embeddingCoverage = (stats.totalEmbeddings / stats.totalGuidelines * 100).toFixed(1);
      console.log(`  - Embedding coverage: ${embeddingCoverage}%`);
      
      if (stats.totalEmbeddings >= stats.totalGuidelines) {
        console.log('✅ Database is healthy and ready for OpenAI Agents SDK');
      } else {
        console.log('⚠️  Some guidelines are missing embeddings');
      }
      
    } catch (error) {
      console.error('❌ Database health check failed:', error);
    }
  }
}

// Main execution
async function main() {
  console.log('🔧 Vector Performance Optimization for OpenAI Agents SDK');
  console.log('=' .repeat(60));
  
  const optimizer = new VectorPerformanceOptimizer();
  
  try {
    // Check database health first
    await optimizer.checkDatabaseHealth();
    console.log('');
    
    // Optimize vector performance
    await optimizer.optimizeVectorIndexes();
    
    console.log('\n🎉 Optimization completed successfully!');
    console.log('💡 Your database is now optimized for OpenAI Agents SDK');
    
  } catch (error) {
    console.error('💥 Optimization failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
    await dbUtils.closePool();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { VectorPerformanceOptimizer };
