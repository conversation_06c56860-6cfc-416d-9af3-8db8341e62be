# Scripts Directory

This directory contains all utility scripts for the Dental Narrator project. Each script serves a specific purpose in data management, processing, and system maintenance.

## 📁 Script Overview

### Data Import & Processing
- **`import-csv-data.ts`** - Imports CSV data (carriers, procedures, documentation requirements) into the database
- **`seed-database.ts`** - Seeds the database with initial data and schema setup

### Data Quality & Validation
- **`validate-clean-data.ts`** - Validates data quality and performs basic cleaning operations
- **`fix-data-quality-issues.ts`** - Fixes specific data quality issues identified during validation

### AI & Embeddings
- **`test-embeddings.ts`** - Tests OpenAI embedding generation and vector storage functionality
- **`generate-embeddings.ts`** - Generates embeddings for all guidelines to enable semantic search
- **`optimize-vector-indexes.ts`** - Creates optimized vector search indexes for fast similarity search
- **`test-vector-search.ts`** - Tests vector search functionality and performance benchmarking

## 🚀 Usage

All scripts can be run using npm commands defined in `package.json`:

```bash
# Data Import
npm run import-csv          # Import CSV data
npm run seed-database       # Seed database with initial data

# Data Quality
npm run validate-data       # Validate and clean data
npm run fix-data-quality    # Fix specific data quality issues

# AI & Embeddings
npm run test-embeddings        # Test embedding system
npm run generate-embeddings    # Generate embeddings for all guidelines
npm run optimize-vector-indexes # Create optimized vector search indexes
npm run test-vector-search     # Test vector search performance

# Development
npm run test-db            # Test database connection
```

## 📋 Prerequisites

Before running any scripts, ensure you have:

1. **Environment Variables** set in `.env.development`:
   ```
   OPENAI_API_KEY=your_openai_api_key
   POSTGRES_CONNECTION_STRING=your_supabase_connection_string
   ```

2. **Dependencies** installed:
   ```bash
   npm install
   ```

3. **Database** properly configured with required tables and extensions

## 🔄 Typical Workflow

1. **Initial Setup**:
   ```bash
   npm run seed-database      # Set up database schema
   npm run import-csv         # Import CSV data
   ```

2. **Data Quality**:
   ```bash
   npm run validate-data      # Check data quality
   npm run fix-data-quality   # Fix any issues
   ```

3. **AI Features**:
   ```bash
   npm run test-embeddings    # Test embedding system
   npm run generate-embeddings # Generate all embeddings
   ```

## 📊 Script Details

### import-csv-data.ts
- **Purpose**: Import carrier, procedure, and documentation data from CSV files
- **Input**: CSV files in `assets/` directory
- **Output**: Populated database tables
- **Features**: Batch processing, duplicate detection, data normalization

### validate-clean-data.ts
- **Purpose**: Comprehensive data validation and basic cleaning
- **Checks**: Duplicates, missing data, format validation, cross-table consistency
- **Output**: Detailed validation report with issue counts and samples

### fix-data-quality-issues.ts
- **Purpose**: Fix specific data quality issues
- **Actions**: Update missing contact info, fix procedure descriptions, create aliases
- **Output**: Summary of fixes applied and final data quality metrics

### generate-embeddings.ts
- **Purpose**: Generate OpenAI embeddings for all guidelines
- **Features**: Intelligent batch processing, token management, content truncation, rate limiting, error handling, progress tracking
- **Model**: text-embedding-3-small (1536 dimensions)
- **Batch Size**: 5 guidelines per batch (optimized for token limits)
- **Token Management**: Automatic content truncation for oversized content (>7000 tokens)
- **Fallback Processing**: Individual processing for large batches that exceed token limits
- **Rate Limiting**: 1.5 second delays between batches to respect API limits
- **Error Handling**: Zero-vector fallbacks for failed embeddings, comprehensive retry logic
- **Output**: Vector embeddings stored in database for semantic search

### test-embeddings.ts
- **Purpose**: Test embedding generation system before full processing
- **Tests**: API connection, database storage, vector operations
- **Output**: Validation that embedding system is ready for production use

### optimize-vector-indexes.ts
- **Purpose**: Create and optimize vector search indexes for fast similarity search
- **Features**: IVFFlat index optimization, supporting indexes for hybrid search, performance analysis
- **Indexes Created**: Content-type indexes, metadata indexes (carrier, category), GIN indexes for JSONB
- **Functions Created**: `search_guidelines_vector()`, `search_guidelines_hybrid()`
- **Performance**: Achieves <500ms search times for 2,000+ guidelines
- **Output**: Optimized database indexes and search functions for semantic search

### test-vector-search.ts
- **Purpose**: Comprehensive testing of vector search functionality and performance
- **Tests**: Basic vector search, hybrid search (similarity + recency), filtered search, performance benchmarking
- **Performance Metrics**: Average search time, min/max times, similarity scoring validation
- **Query Types**: Natural language queries converted to embeddings for semantic search
- **Output**: Performance validation and search quality verification

## 🛠️ Development Notes

- All scripts use TypeScript and can be run with `ts-node`
- Scripts follow consistent error handling and logging patterns
- Database connections use connection pooling for efficiency
- Rate limiting is implemented for external API calls
- Progress tracking and detailed logging for long-running operations

## 🔧 Troubleshooting

**Common Issues**:
- Missing environment variables → Check `.env.development`
- Database connection errors → Verify connection string and network access
- OpenAI API errors → Check API key and rate limits
- TypeScript errors → Ensure all dependencies are installed

**Logs**: All scripts provide detailed console output for debugging and monitoring progress.
