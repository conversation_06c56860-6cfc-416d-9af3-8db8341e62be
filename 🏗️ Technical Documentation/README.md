# 🏗️ Technical Documentation Hub

*Technical architecture and implementation documentation for Dental Narrator Beta*

## 📚 **Technical Architecture**

### Core System Components
- [System Architecture Overview](./System Architecture.md)
- [Database Schema & Design](./Database Design.md)
- [API Specification](./API Documentation.md)
- [Integration Architecture](./Integration Architecture.md)

### Implementation Details
- [Crawl4AI MCP Integration](./Crawl4AI Integration.md)
- [Vector Database Implementation](./Vector Database.md)
- [FastAPI Backend Design](./FastAPI Backend.md)
- [Authentication & Security](./Security Implementation.md)

### Development Resources
- [Development Environment Setup](./Development Setup.md)
- [Deployment Architecture](./Deployment Architecture.md)
- [Testing Strategy](./Testing Strategy.md)
- [Performance Optimization](./Performance Optimization.md)

---

## 🔧 **Current Technical Status**

### Foundation Requirements
- **Crawl4AI Fork**: AOJ-74 - FOUNDATIONAL BLOCKER
- **Vector Embeddings**: AOJ-87 - Due 2025-06-25 🔴
- **FastAPI Backend**: AOJ-88 - Due 2025-06-27
- **BMAD Prompt Integration**: AOJ-89 - Due 2025-06-28

### Technology Stack
```
Frontend: Next.js/React (existing Dental Narrator UI)
Backend: Crawl4AI MCP Server (forked and customized)
Database: Supabase (vector database for RAG)
AI/ML: OpenAI GPT-4 + Custom embeddings
Infrastructure: Docker containerization
API: RESTful + MCP protocol support
```

### Data Infrastructure
- **Guidelines Database**: 2,006 insurance guidelines ready for embeddings
- **Carriers Supported**: Aetna, Cigna, MetLife, UHC (initial targets)
- **Vector Search**: pgvector extension installed, embeddings pending
- **Real-time Updates**: Automated policy change detection pipeline planned

---

## 🔗 **Linear Integration**

**Project Link**: [Dental Narrator Beta](https://linear.app/aojdevstudio/project/dental-narrator-beta-7b95ff168151)

**Critical Path Issues**:
1. **AOJ-74**: Fork Crawl4AI Repository (enables all other work)
2. **AOJ-87**: Generate Vector Embeddings (activates search capabilities)
3. **AOJ-88**: FastAPI Backend (enables dynamic data access)
4. **AOJ-89**: BMAD Prompt Architecture (improves response quality)

---

*Technical implementation documentation supporting the transformation from Custom GPT to enterprise SaaS platform*
