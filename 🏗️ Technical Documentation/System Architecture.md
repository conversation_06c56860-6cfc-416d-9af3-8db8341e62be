# 🏗️ System Architecture Overview

*High-level architecture for Dental Narrator Beta transformation from Custom GPT to SaaS*

## 🎯 **Architecture Goals**

### Business Objectives
- **Scale from Custom GPT to SaaS**: Support 10,000+ dental practices
- **Real-time Intelligence**: Replace static data with dynamic insurance updates
- **Revenue Generation**: Multi-tier subscription model ($149-999/month)

### Technical Objectives  
- **High Availability**: 99.9% uptime SLA
- **Fast Response Times**: <2 seconds for narrative generation
- **Scalable Vector Search**: 2,006+ guidelines with sub-second retrieval
- **Real-time Updates**: Automated policy change detection and integration

---

## 🏛️ **High-Level Architecture**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │  Crawl4AI MCP   │    │   Supabase      │
│   (Next.js)     │───▶│   Server        │───▶│   Database      │
│                 │    │                 │    │   + pgvector    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   External      │
                       │   Insurance     │
                       │   Sources       │
                       └─────────────────┘
```

### Core Components
1. **Frontend Application**: Enhanced Dental Narrator UI (Next.js/React)
2. **Crawl4AI MCP Server**: Custom fork for insurance data processing
3. **Vector Database**: Supabase with pgvector for RAG capabilities  
4. **External Integrations**: Real-time insurance policy monitoring

---

## 🔧 **Component Details**

### 1. Frontend Layer
- **Technology**: Next.js 14+ with App Router
- **Authentication**: Supabase Auth + subscription management
- **UI Framework**: Tailwind CSS + shadcn/ui components
- **State Management**: Zustand for client state
- **Real-time Updates**: Supabase Realtime subscriptions

### 2. Crawl4AI MCP Server (Core Innovation)
```
Crawl4AI MCP Server
├── Insurance Data Crawler
│   ├── Aetna Policy Monitor
│   ├── Cigna Updates Tracker  
│   ├── MetLife Guidelines Scanner
│   └── UHC Policy Watcher
├── Vector Processing Pipeline
│   ├── Document Chunking
│   ├── Embedding Generation (OpenAI)
│   └── Vector Store Updates
└── MCP Protocol Interface
    ├── Resource Endpoints
    ├── Tool Functions
    └── Prompt Templates
```

### 3. Database Architecture
```sql
-- Core Tables
guidelines (id, carrier, procedure_code, content, updated_at)
embeddings (guideline_id, vector, metadata)
practices (id, subscription_tier, api_usage)
narratives (id, practice_id, procedure, content, confidence_score)

-- Vector Search
CREATE INDEX idx_embeddings_vector ON embeddings 
USING ivfflat (vector vector_cosine_ops);
```

### 4. External Integration Layer
- **Insurance Carriers**: Direct API connections where available
- **Web Scraping**: Crawl4AI for policy change detection
- **Data Validation**: Cross-reference multiple sources
- **Update Pipeline**: Automated change detection and processing

---

## 🚀 **Scalability Architecture**

### Horizontal Scaling Strategy
```
Load Balancer
├── Crawl4AI Instance 1 (Primary)
├── Crawl4AI Instance 2 (Backup)
└── Crawl4AI Instance N (Auto-scale)

Database Scaling
├── Primary Supabase Instance
├── Read Replicas (geo-distributed)
└── Vector Index Optimization
```

### Performance Optimization
- **Caching Strategy**: Redis for frequently accessed guidelines
- **CDN Distribution**: Static assets and cached responses  
- **Vector Index Tuning**: Optimized similarity search parameters
- **Connection Pooling**: Efficient database connection management

---

## 🔒 **Security Architecture**

### Data Protection
- **Encryption**: TLS 1.3 in transit, AES-256 at rest
- **Authentication**: JWT tokens + refresh token rotation
- **Authorization**: Role-based access control (RBAC)
- **Audit Logging**: Comprehensive activity tracking

### Compliance Framework
- **HIPAA Readiness**: PHI handling protocols
- **SOC 2 Type II**: Security controls and monitoring
- **Data Residency**: Geographic data placement controls
- **Backup Strategy**: Point-in-time recovery + disaster recovery

---

## 📊 **Monitoring & Observability**

### System Monitoring
- **Application Performance**: Response times, error rates
- **Database Performance**: Query optimization, connection health
- **Vector Search Metrics**: Search accuracy, embedding quality  
- **External API Health**: Insurance source availability

### Business Metrics
- **User Engagement**: Narrative generation frequency
- **API Usage**: Per-practice consumption tracking
- **Revenue Metrics**: Subscription tier distribution
- **Customer Success**: Claim approval rate improvements

---

## 🛣️ **Implementation Priority**

### Phase 1: Foundation (Current)
- ✅ Supabase database setup
- 🔄 **AOJ-74**: Fork Crawl4AI repository (CRITICAL BLOCKER)
- 🔄 **AOJ-87**: Generate vector embeddings for 2,006 guidelines

### Phase 2: Core Platform  
- 🔄 **AOJ-88**: FastAPI backend integration
- 🔄 **AOJ-89**: BMAD-enhanced prompt architecture
- 🔄 Subscription management system

### Phase 3: Scale & Optimize
- Real-time policy update pipeline
- Advanced vector search optimization
- Enterprise features and integrations

---

*This architecture supports the evolution from Custom GPT prototype to enterprise-grade SaaS platform serving 10,000+ dental practices.*
