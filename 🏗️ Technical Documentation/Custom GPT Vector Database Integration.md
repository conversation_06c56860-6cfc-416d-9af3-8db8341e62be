# Custom GPT + Vector Database Integration Architecture

> **Created:** [[2025-06-22]]  
> **Tags:** #architecture #integration #supabase #fastapi #vector-database  
> **Parent Project:** [[unified-dental-projects]]  
> **Related:** [[frameworks/BMAD Dental Office Template Framework]]

---

## Overview

Integration strategy for connecting BMAD-powered Custom GPTs with Supabase vector database containing insurance embeddings. This architecture transforms static knowledge base GPTs into dynamic, real-time intelligence systems.

## Current State vs Target State

### **Current State**
- Custom GPT with static text documents in knowledge base
- Limited to uploaded file content
- No real-time data access
- Manual updates required

### **Target State** 
- Custom GPT with dynamic vector database access
- Real-time insurance information retrieval
- Semantic search across comprehensive insurance data
- Auto-updating knowledge base

---

## Recommended Architecture

### **High-Level Architecture**
```mermaid
graph TB
    A[Custom GPT] --> B[FastAPI Backend on Replit]
    B --> C[Supabase Vector Database]
    B --> D[Supabase Postgres DB]
    C --> E[Insurance Embeddings]
    D --> F[Structured Insurance Data]
    A --> G[Team Button Interface]
    G --> H[Insurance Verification Results]
```

### **Component Breakdown**

#### **1. Custom GPT Layer**
- **BMAD-enhanced prompts** with vector database integration
- **Custom Actions** for API calls to FastAPI backend
- **Team-friendly interface** with button workflows
- **Response enhancement** using retrieved vector data

#### **2. FastAPI Middleware (Replit)**
- **Authentication** and security layer
- **Query processing** and vector search orchestration
- **Data formatting** for GPT consumption
- **Caching** for performance optimization
- **Error handling** and fallback mechanisms

#### **3. Supabase Backend**
- **Vector database** with pgvector for semantic search
- **Structured data** in Postgres for exact lookups
- **Real-time subscriptions** for data updates
- **Row Level Security** for data protection

---

## Implementation Plan

### **Phase 1: FastAPI Backend Setup**

#### **Core FastAPI Structure**
```python
# main.py - FastAPI Backend
from fastapi import FastAPI, HTTPException, Depends
from supabase import create_client, Client
import openai
from typing import List, Optional
import os

app = FastAPI(title="Dental Insurance API", version="1.0.0")

# Supabase connection
supabase: Client = create_client(
    os.getenv("SUPABASE_URL"),
    os.getenv("SUPABASE_KEY")
)

@app.post("/search-insurance")
async def search_insurance_info(
    query: str,
    carrier: Optional[str] = None,
    procedure_code: Optional[str] = None,
    limit: int = 5
):
    """
    Semantic search for insurance information
    Integrates with BMAD Custom GPT actions
    """
    try:
        # Generate embedding for query
        embedding = await generate_embedding(query)
        
        # Search vector database
        results = await search_vector_db(
            embedding=embedding,
            carrier=carrier,
            procedure_code=procedure_code,
            limit=limit
        )
        
        # Format for GPT consumption
        formatted_results = format_for_gpt(results)
        
        return {
            "success": True,
            "results": formatted_results,
            "metadata": {
                "query": query,
                "carrier": carrier,
                "procedure_code": procedure_code
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/verify-coverage")
async def verify_coverage(
    carrier: str,
    procedure_codes: List[str],
    patient_info: dict
):
    """
    Real-time coverage verification
    Enhanced with vector similarity search
    """
    # Implementation for live verification
    pass

@app.post("/get-documentation-requirements")
async def get_documentation_requirements(
    carrier: str,
    procedure_codes: List[str]
):
    """
    Get exact documentation requirements
    Supports BMAD precision mandate
    """
    # Implementation for exact requirements
    pass
```

#### **Supabase Integration Functions**
```python
# supabase_client.py
async def search_vector_db(embedding, carrier=None, procedure_code=None, limit=5):
    """
    Semantic search in Supabase vector database
    """
    query = supabase.table('insurance_embeddings').select(
        'content, metadata, similarity'
    ).rpc(
        'match_insurance_documents',
        {
            'query_embedding': embedding,
            'match_threshold': 0.7,
            'match_count': limit
        }
    )
    
    # Add filters if provided
    if carrier:
        query = query.eq('metadata->carrier', carrier)
    if procedure_code:
        query = query.eq('metadata->procedure_code', procedure_code)
    
    result = query.execute()
    return result.data

async def get_structured_data(table: str, filters: dict):
    """
    Get exact data from structured tables
    """
    query = supabase.table(table).select('*')
    
    for key, value in filters.items():
        query = query.eq(key, value)
    
    result = query.execute()
    return result.data
```

### **Phase 2: Custom GPT Actions Configuration**

#### **Action Schema for Custom GPT**
```json
{
  "openapi": "3.0.0",
  "info": {
    "title": "Dental Insurance API",
    "version": "1.0.0"
  },
  "servers": [
    {
      "url": "https://your-replit-app.replit.dev"
    }
  ],
  "paths": {
    "/search-insurance": {
      "post": {
        "operationId": "searchInsuranceInfo",
        "summary": "Search insurance information using semantic search",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "query": {"type": "string"},
                  "carrier": {"type": "string"},
                  "procedure_code": {"type": "string"},
                  "limit": {"type": "integer", "default": 5}
                },
                "required": ["query"]
              }
            }
          }
        }
      }
    },
    "/verify-coverage": {
      "post": {
        "operationId": "verifyCoverage",
        "summary": "Verify real-time coverage for procedures",
        "requestBody": {
          "required": true,
          "content": {
            "application/json": {
              "schema": {
                "type": "object",
                "properties": {
                  "carrier": {"type": "string"},
                  "procedure_codes": {
                    "type": "array",
                    "items": {"type": "string"}
                  },
                  "patient_info": {"type": "object"}
                },
                "required": ["carrier", "procedure_codes"]
              }
            }
          }
        }
      }
    }
  }
}
```

### **Phase 3: Enhanced BMAD Prompts**

#### **Vector-Enhanced Insurance Agent**
```yaml
# Enhanced BMAD Prompt with Vector Integration
agent:
  name: Vector-Enhanced-Insurance-Verifier
  capabilities:
    - real_time_vector_search
    - semantic_insurance_lookup
    - dynamic_knowledge_retrieval
    - structured_data_access

enhanced_workflow:
  1. "Receive user query about insurance coverage"
  2. "Use searchInsuranceInfo action to get semantic matches"
  3. "Use verifyCoverage action for real-time verification"
  4. "Apply BMAD precision rules to format response"
  5. "Provide definitive answer with vector-enhanced accuracy"

integration_instructions: |
  When processing insurance queries:
  1. ALWAYS call searchInsuranceInfo first for comprehensive context
  2. For specific coverage questions, use verifyCoverage action
  3. Combine vector results with BMAD precision rules
  4. Format using mandatory checklist structure
  5. Include vector search confidence scores in metadata
```

---

## Technical Implementation Details

### **Supabase Vector Database Schema**
```sql
-- Insurance embeddings table
CREATE TABLE insurance_embeddings (
  id BIGSERIAL PRIMARY KEY,
  content TEXT NOT NULL,
  embedding VECTOR(1536), -- OpenAI ada-002 dimensions
  metadata JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create vector similarity search function
CREATE OR REPLACE FUNCTION match_insurance_documents(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.7,
  match_count INT DEFAULT 5
)
RETURNS TABLE(
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE SQL STABLE
AS $$
  SELECT
    content,
    metadata,
    1 - (embedding <=> query_embedding) AS similarity
  FROM insurance_embeddings
  WHERE 1 - (embedding <=> query_embedding) > match_threshold
  ORDER BY embedding <=> query_embedding
  LIMIT match_count;
$$;

-- Structured insurance data tables
CREATE TABLE carriers (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  guidelines_version VARCHAR(100),
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE procedure_requirements (
  id SERIAL PRIMARY KEY,
  carrier_id INT REFERENCES carriers(id),
  procedure_code VARCHAR(10) NOT NULL,
  frequency_limit VARCHAR(100),
  documentation_required JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **Replit Deployment Configuration**
```python
# replit.nix
{ pkgs }: {
  deps = [
    pkgs.python310Full
    pkgs.poetry
  ];
}

# pyproject.toml
[tool.poetry.dependencies]
python = "^3.10"
fastapi = "^0.104.1"
uvicorn = "^0.24.0"
supabase = "^2.0.0"
openai = "^1.3.0"
python-multipart = "^0.0.6"
pydantic = "^2.4.0"

# main entry point for Replit
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

---

## Integration Benefits

### **For BMAD Framework**
- **Enhanced Accuracy**: Vector search provides contextually relevant insurance information
- **Real-Time Data**: Live access to current insurance rules and requirements
- **Scalable Knowledge**: No need to manually update GPT knowledge base
- **Precision Support**: Vector results enhance BMAD's precision mandate

### **For Dental Office Team**
- **Same Simple Interface**: Team still sees button-driven workflows
- **Better Results**: More accurate, up-to-date insurance information
- **Faster Processing**: Semantic search faster than manual lookup
- **Consistent Quality**: Vector database ensures comprehensive coverage

### **For System Reliability**
- **Fallback Mechanisms**: If API fails, GPT uses built-in knowledge
- **Caching**: FastAPI caches common queries for speed
- **Error Handling**: Graceful degradation if vector search unavailable
- **Security**: Supabase RLS protects sensitive insurance data

---

## Implementation Roadmap

### **Week 1: Backend Foundation**
- [ ] Set up FastAPI project on Replit
- [ ] Configure Supabase connection
- [ ] Implement basic vector search endpoints
- [ ] Test API functionality

### **Week 2: Custom GPT Integration**
- [ ] Configure Custom Actions in ChatGPT
- [ ] Test API calls from Custom GPT
- [ ] Implement error handling and fallbacks
- [ ] Optimize response formatting

### **Week 3: BMAD Enhancement**
- [ ] Update BMAD prompts with vector integration
- [ ] Test enhanced insurance verification workflows
- [ ] Implement team-friendly button interfaces
- [ ] Performance testing and optimization

### **Week 4: Production Deployment**
- [ ] Security audit and hardening
- [ ] Production environment setup
- [ ] Team training and rollout
- [ ] Monitoring and analytics setup

---

## Security Considerations

### **API Security**
- **Authentication**: API key-based authentication for GPT actions
- **Rate Limiting**: Prevent abuse and control costs
- **Input Validation**: Sanitize all inputs to prevent injection attacks
- **HTTPS**: Encrypted communication between GPT and API

### **Data Protection**
- **Supabase RLS**: Row-level security for sensitive insurance data
- **Environment Variables**: Secure storage of API keys and secrets
- **Audit Logging**: Track all API calls and data access
- **HIPAA Compliance**: Ensure patient data protection standards

---

## Cost Optimization

### **API Costs**
- **Caching Strategy**: Cache common queries to reduce API calls
- **Batch Processing**: Group similar queries when possible
- **Usage Monitoring**: Track and optimize expensive operations
- **Fallback Logic**: Use local knowledge when appropriate

### **Vector Database Costs**
- **Embedding Efficiency**: Optimize embedding generation and storage
- **Query Optimization**: Fine-tune search parameters for cost/accuracy balance
- **Data Lifecycle**: Archive old embeddings to control storage costs

---

## Next Steps

1. **Proof of Concept**: Start with simple FastAPI + Supabase integration
2. **Custom Action Testing**: Test basic API calls from Custom GPT
3. **BMAD Integration**: Enhance existing prompts with vector capabilities
4. **Team Validation**: Test with real insurance verification scenarios
5. **Production Scaling**: Optimize for performance and reliability

This architecture transforms your BMAD-powered Custom GPTs from static tools into dynamic, intelligent systems with real-time access to comprehensive insurance data while maintaining the team-friendly interfaces your staff needs.

---

## Related Projects
- **BMAD Framework**: [[frameworks/BMAD Dental Office Template Framework]]
- **Legacy Integration**: [[legacy-projects/Legacy Projects Analysis]]
- **Technical Infrastructure**: [[unified-dental-projects/README]]
