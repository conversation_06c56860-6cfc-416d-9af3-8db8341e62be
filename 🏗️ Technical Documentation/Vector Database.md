# 🔍 Vector Database Implementation

*Vector search architecture for 2,006+ insurance guidelines - AOJ-87 Implementation*

## 🎯 **Implementation Objectives**

### Primary Goals
- **Vector Embeddings**: Generate embeddings for 2,006 existing insurance guidelines
- **Sub-second Search**: Achieve <500ms similarity search response times
- **Scalable Architecture**: Support 10,000+ guidelines with room for growth
- **High Accuracy**: 90%+ relevance in top-3 search results

### Business Impact
- **Real-time Narrative Generation**: Instant access to relevant guidelines
- **Improved Accuracy**: Context-aware insurance policy matching
- **Cost Efficiency**: Reduced manual research time for dental practices
- **Competitive Advantage**: Dynamic data vs. static competitor solutions

---

## 🏗️ **Database Architecture**

### Supabase + pgvector Setup
```sql
-- Enable vector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Guidelines table with vector support
CREATE TABLE guidelines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    carrier VARCHAR(50) NOT NULL,
    procedure_code VARCHAR(20),
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    effective_date DATE,
    updated_at TIMESTAMP DEFAULT NOW(),
    source_url TEXT,
    confidence_score DECIMAL(3,2) DEFAULT 1.0,
    
    -- Metadata for better search
    category VARCHAR(100),
    subcategory VARCHAR(100),
    keywords TEXT[],
    
    -- Vector embedding (OpenAI ada-002: 1536 dimensions)
    embedding vector(1536),
    
    -- Indexes
    CONSTRAINT valid_confidence CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0)
);

-- Vector similarity index (IVFFlat for large datasets)
CREATE INDEX guidelines_embedding_idx ON guidelines 
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Additional indexes for metadata filtering
CREATE INDEX idx_guidelines_carrier ON guidelines(carrier);
CREATE INDEX idx_guidelines_procedure ON guidelines(procedure_code);
CREATE INDEX idx_guidelines_category ON guidelines(category);
CREATE INDEX idx_guidelines_updated ON guidelines(updated_at);
```

### Vector Search Performance Optimization
```sql
-- Hybrid search combining vector similarity + metadata filtering
CREATE OR REPLACE FUNCTION search_guidelines(
    query_embedding vector(1536),
    carrier_filter TEXT DEFAULT NULL,
    procedure_filter TEXT DEFAULT NULL,
    similarity_threshold FLOAT DEFAULT 0.7,
    max_results INT DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    carrier VARCHAR(50),
    procedure_code VARCHAR(20),
    similarity_score FLOAT,
    rank_score FLOAT
)
LANGUAGE sql
AS $$
    SELECT 
        g.id,
        g.title,
        g.content,
        g.carrier,
        g.procedure_code,
        (1 - (g.embedding <=> query_embedding)) as similarity_score,
        -- Composite ranking: similarity + recency + confidence
        (
            (1 - (g.embedding <=> query_embedding)) * 0.7 +
            (EXTRACT(days FROM (NOW() - g.updated_at)) / 365.0) * 0.2 +
            g.confidence_score * 0.1
        ) as rank_score
    FROM guidelines g
    WHERE 
        (1 - (g.embedding <=> query_embedding)) > similarity_threshold
        AND (carrier_filter IS NULL OR g.carrier = carrier_filter)
        AND (procedure_filter IS NULL OR g.procedure_code = procedure_filter)
    ORDER BY rank_score DESC
    LIMIT max_results;
$$;
```

---

## 🤖 **Embedding Generation Pipeline**

### OpenAI Integration
```python
# Vector embedding generation service
import openai
from typing import List, Dict
import asyncio
from supabase import create_client

class GuidelineEmbeddingService:
    def __init__(self, openai_key: str, supabase_url: str, supabase_key: str):
        self.openai_client = openai.AsyncOpenAI(api_key=openai_key)
        self.supabase = create_client(supabase_url, supabase_key)
        
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text using OpenAI ada-002"""
        response = await self.openai_client.embeddings.create(
            model="text-embedding-ada-002",
            input=text,
            encoding_format="float"
        )
        return response.data[0].embedding
    
    async def process_guideline(self, guideline_id: str) -> bool:
        """Process a single guideline to generate and store embedding"""
        try:
            # Fetch guideline content
            result = self.supabase.table('guidelines').select(
                'id, title, content, summary'
            ).eq('id', guideline_id).single().execute()
            
            guideline = result.data
            
            # Create enhanced text for embedding
            enhanced_text = self.create_enhanced_text(guideline)
            
            # Generate embedding
            embedding = await self.generate_embedding(enhanced_text)
            
            # Update database with embedding
            self.supabase.table('guidelines').update({
                'embedding': embedding
            }).eq('id', guideline_id).execute()
            
            return True
            
        except Exception as e:
            print(f"Error processing guideline {guideline_id}: {e}")
            return False
    
    def create_enhanced_text(self, guideline: Dict) -> str:
        """Create optimized text for embedding generation"""
        # Combine title, summary, and key content sections
        parts = [
            f"Title: {guideline['title']}",
            f"Summary: {guideline.get('summary', '')}",
            f"Content: {guideline['content'][:2000]}"  # Truncate long content
        ]
        return "\n\n".join(filter(None, parts))

# Batch processing for 2,006 guidelines
async def process_all_guidelines():
    """AOJ-87: Generate embeddings for all 2,006 guidelines"""
    service = GuidelineEmbeddingService(
        openai_key=os.getenv('OPENAI_API_KEY'),
        supabase_url=os.getenv('SUPABASE_URL'),
        supabase_key=os.getenv('SUPABASE_SERVICE_KEY')
    )
    
    # Fetch all guidelines without embeddings
    result = service.supabase.table('guidelines').select('id').is_('embedding', 'null').execute()
    guideline_ids = [row['id'] for row in result.data]
    
    print(f"Processing {len(guideline_ids)} guidelines...")
    
    # Process in batches to avoid rate limits
    batch_size = 10
    for i in range(0, len(guideline_ids), batch_size):
        batch = guideline_ids[i:i + batch_size]
        tasks = [service.process_guideline(gid) for gid in batch]
        results = await asyncio.gather(*tasks)
        
        success_count = sum(results)
        print(f"Batch {i//batch_size + 1}: {success_count}/{len(batch)} successful")
        
        # Rate limiting delay
        await asyncio.sleep(1)
    
    print("✅ All guidelines processed!")
```

### Data Quality & Validation
```python
# Vector quality validation
class EmbeddingQualityValidator:
    def __init__(self, supabase_client):
        self.supabase = supabase_client
    
    def validate_embedding_coverage(self) -> Dict:
        """Ensure all guidelines have valid embeddings"""
        total_result = self.supabase.table('guidelines').select('id', count='exact').execute()
        embedded_result = self.supabase.table('guidelines').select('id', count='exact').not_.is_('embedding', 'null').execute()
        
        return {
            'total_guidelines': total_result.count,
            'embedded_guidelines': embedded_result.count,
            'coverage_percentage': (embedded_result.count / total_result.count) * 100,
            'missing_embeddings': total_result.count - embedded_result.count
        }
    
    def test_search_quality(self, test_queries: List[str]) -> Dict:
        """Test search quality with known queries"""
        results = {}
        for query in test_queries:
            # Generate query embedding
            embedding = generate_embedding(query)
            
            # Perform search
            search_results = self.supabase.rpc('search_guidelines', {
                'query_embedding': embedding,
                'max_results': 5
            }).execute()
            
            results[query] = {
                'result_count': len(search_results.data),
                'top_similarity': search_results.data[0]['similarity_score'] if search_results.data else 0,
                'avg_similarity': sum(r['similarity_score'] for r in search_results.data) / len(search_results.data) if search_results.data else 0
            }
        
        return results
```

---

## ⚡ **Performance Optimization**

### Search Performance Targets
- **Response Time**: <500ms for similarity search
- **Throughput**: 1,000+ concurrent searches
- **Accuracy**: 90%+ relevance in top-3 results
- **Scalability**: Support 10,000+ guidelines

### Optimization Strategies
```sql
-- 1. Optimized vector index parameters
DROP INDEX IF EXISTS guidelines_embedding_idx;
CREATE INDEX guidelines_embedding_idx ON guidelines 
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 200);  -- Increased for better accuracy

-- 2. Materialized view for frequent searches
CREATE MATERIALIZED VIEW popular_procedures AS
SELECT 
    procedure_code,
    COUNT(*) as guideline_count,
    ARRAY_AGG(DISTINCT carrier) as carriers,
    AVG(confidence_score) as avg_confidence
FROM guidelines 
WHERE procedure_code IS NOT NULL
GROUP BY procedure_code
HAVING COUNT(*) > 5
ORDER BY guideline_count DESC;

-- 3. Partial indexes for common filters
CREATE INDEX idx_guidelines_aetna_recent ON guidelines(updated_at DESC)
WHERE carrier = 'Aetna' AND updated_at > (NOW() - INTERVAL '1 year');

CREATE INDEX idx_guidelines_high_confidence ON guidelines(confidence_score DESC)
WHERE confidence_score > 0.8;
```

### Caching Strategy
```python
# Redis caching for frequent searches
import redis
import json
import hashlib

class VectorSearchCache:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.ttl = 3600  # 1 hour cache
    
    def cache_key(self, query_text: str, filters: Dict) -> str:
        """Generate cache key for search query"""
        cache_data = {
            'query': query_text,
            'filters': sorted(filters.items())
        }
        cache_string = json.dumps(cache_data, sort_keys=True)
        return f"search:{hashlib.md5(cache_string.encode()).hexdigest()}"
    
    def get_cached_results(self, query_text: str, filters: Dict) -> List[Dict]:
        """Retrieve cached search results"""
        key = self.cache_key(query_text, filters)
        cached = self.redis.get(key)
        return json.loads(cached) if cached else None
    
    def cache_results(self, query_text: str, filters: Dict, results: List[Dict]):
        """Cache search results"""
        key = self.cache_key(query_text, filters)
        self.redis.setex(key, self.ttl, json.dumps(results))
```

---

## 📊 **Monitoring & Analytics**

### Vector Search Metrics
```python
# Search analytics tracking
class VectorSearchAnalytics:
    def track_search(self, query: str, results: List[Dict], user_id: str = None):
        """Track search performance and user behavior"""
        analytics_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'query_length': len(query),
            'result_count': len(results),
            'top_similarity_score': results[0]['similarity_score'] if results else 0,
            'avg_similarity_score': sum(r['similarity_score'] for r in results) / len(results) if results else 0,
            'user_id': user_id,
            'carriers_in_results': list(set(r['carrier'] for r in results)),
            'procedures_in_results': list(set(r['procedure_code'] for r in results if r['procedure_code']))
        }
        
        # Store in analytics table
        self.supabase.table('search_analytics').insert(analytics_data).execute()
    
    def get_search_performance_metrics(self, days: int = 7) -> Dict:
        """Get search performance metrics for monitoring"""
        cutoff_date = (datetime.utcnow() - timedelta(days=days)).isoformat()
        
        result = self.supabase.table('search_analytics').select(
            'avg_similarity_score, result_count, query_length'
        ).gte('timestamp', cutoff_date).execute()
        
        data = result.data
        return {
            'total_searches': len(data),
            'avg_similarity': sum(r['avg_similarity_score'] for r in data) / len(data) if data else 0,
            'avg_results_per_search': sum(r['result_count'] for r in data) / len(data) if data else 0,
            'avg_query_length': sum(r['query_length'] for r in data) / len(data) if data else 0
        }
```

---

## 🚀 **Implementation Timeline**

### Week 1: Foundation (AOJ-87)
- ✅ Database schema and indexes
- 🔄 **Embedding generation pipeline setup**
- 🔄 **Batch processing all 2,006 guidelines**
- 🔄 **Quality validation and testing**

### Week 2: Optimization  
- Search performance tuning
- Caching layer implementation
- Monitoring and analytics setup

### Week 3: Integration
- Crawl4AI MCP server integration
- FastAPI backend connection (AOJ-88)
- Frontend search interface updates

---

## ✅ **Success Metrics**

### Technical KPIs
- **Embedding Coverage**: 100% of 2,006 guidelines
- **Search Response Time**: <500ms average
- **Search Accuracy**: 90%+ user satisfaction
- **System Uptime**: 99.9% availability

### Business KPIs  
- **User Engagement**: 50%+ increase in narrative generation
- **Accuracy Improvement**: 30%+ reduction in claim denials
- **Customer Satisfaction**: 4.5+ star average rating

---

*Vector database implementation enabling real-time, intelligent insurance guideline search for the Dental Narrator SaaS platform.*
