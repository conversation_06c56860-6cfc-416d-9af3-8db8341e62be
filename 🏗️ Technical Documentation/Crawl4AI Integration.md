# 🕷️ Crawl4AI MCP Integration

*Foundational integration for AOJ-74: Fork Crawl4AI Repository*

## 🎯 **Integration Objectives**

### Primary Goals  
- **Fork & Customize**: Create dental insurance-specific Crawl4AI implementation
- **MCP Server**: Transform Crawl4AI into Model Context Protocol server
- **Real-time Data**: Replace static 2024 data with dynamic insurance intelligence
- **Scalable Architecture**: Support 10,000+ dental practices

### Business Impact
- **Competitive Advantage**: Real-time data vs. quarterly competitor updates
- **Data Accuracy**: Live insurance policy monitoring and change detection
- **Revenue Enablement**: Foundation for SaaS platform scaling
- **Customer Value**: Reduced claim denials through current policy data

---

## 🏗️ **Crawl4AI MCP Architecture**

### Core MCP Server Design
```
Crawl4AI MCP Server
├── MCP Protocol Layer
│   ├── Resource Handlers (insurance policies, guidelines)
│   ├── Tool Functions (crawl, extract, search)
│   └── Prompt Templates (narrative generation)
├── Insurance Crawler Engine  
│   ├── Carrier-Specific Crawlers
│   ├── Change Detection System
│   └── Data Validation Pipeline
├── Vector Processing Pipeline
│   ├── Content Extraction & Chunking
│   ├── Embedding Generation
│   └── Supabase Vector Store Updates
└── Monitoring & Analytics
    ├── Crawl Performance Metrics
    ├── Data Quality Monitoring
    └── Error Tracking & Alerts
```

### MCP Resource Schema
```json
{
  "resources": {
    "insurance_guidelines": {
      "uri": "dental://guidelines/{carrier}/{procedure}",
      "mimeType": "application/json",
      "description": "Insurance guidelines for specific carrier and procedure"
    },
    "policy_updates": {
      "uri": "dental://updates/{carrier}/latest",
      "mimeType": "application/json", 
      "description": "Recent policy changes and updates"
    },
    "coverage_matrix": {
      "uri": "dental://coverage/{carrier}/matrix",
      "mimeType": "application/json",
      "description": "Comprehensive coverage matrix for carrier"
    }
  },
  "tools": {
    "crawl_carrier_updates": {
      "description": "Crawl specific carrier for policy updates",
      "inputSchema": {
        "type": "object",
        "properties": {
          "carrier": {"type": "string", "enum": ["aetna", "cigna", "metlife", "uhc"]},
          "since": {"type": "string", "format": "date-time"},
          "procedure_codes": {"type": "array", "items": {"type": "string"}}
        }
      }
    },
    "extract_guideline": {
      "description": "Extract specific guideline from carrier website",
      "inputSchema": {
        "type": "object",
        "properties": {
          "url": {"type": "string", "format": "uri"},
          "procedure_code": {"type": "string"},
          "extraction_template": {"type": "string"}
        }
      }
    },
    "search_similar_policies": {
      "description": "Vector search for similar policies across carriers",
      "inputSchema": {
        "type": "object",
        "properties": {
          "query_text": {"type": "string"},
          "carriers": {"type": "array", "items": {"type": "string"}},
          "similarity_threshold": {"type": "number", "minimum": 0, "maximum": 1}
        }
      }
    }
  }
}
```

---

## 🚀 **Fork Implementation Strategy**

### 1. Repository Setup
```bash
# Fork and clone Crawl4AI
git clone https://github.com/unclecode/crawl4ai.git dental-crawl4ai
cd dental-crawl4ai

# Create dental-specific branch
git checkout -b dental-narrator-integration

# Add dental-specific directories
mkdir -p dental_narrator/{
  carriers/{aetna,cigna,metlife,uhc},
  mcp_server,
  templates,
  config,
  tests
}
```

### 2. Carrier-Specific Crawler Extensions
```python
# dental_narrator/carriers/base_carrier.py
from abc import ABC, abstractmethod
from crawl4ai import WebCrawler
from typing import Dict, List, Optional
import asyncio

class BaseInsuranceCarrier(ABC):
    """Base class for carrier-specific crawlers"""
    
    def __init__(self, crawler: WebCrawler):
        self.crawler = crawler
        self.carrier_name = self.get_carrier_name()
        self.base_urls = self.get_base_urls()
        self.selectors = self.get_selectors()
    
    @abstractmethod
    def get_carrier_name(self) -> str:
        pass
    
    @abstractmethod 
    def get_base_urls(self) -> Dict[str, str]:
        pass
    
    @abstractmethod
    def get_selectors(self) -> Dict[str, str]:
        pass
    
    async def crawl_guidelines(self, procedure_codes: List[str] = None) -> List[Dict]:
        """Crawl carrier guidelines for specified procedures"""
        guidelines = []
        
        for section, url in self.base_urls.items():
            result = await self.crawler.arun(
                url=url,
                css_selector=self.selectors.get('guidelines', '.guideline'),
                extraction_strategy=self.get_extraction_strategy()
            )
            
            extracted_guidelines = self.parse_guidelines(result.extracted_content)
            
            # Filter by procedure codes if specified
            if procedure_codes:
                extracted_guidelines = [
                    g for g in extracted_guidelines 
                    if any(code in g.get('content', '') for code in procedure_codes)
                ]
            
            guidelines.extend(extracted_guidelines)
        
        return guidelines
    
    def parse_guidelines(self, content: str) -> List[Dict]:
        """Parse extracted content into structured guidelines"""
        # Carrier-specific parsing logic
        return self._parse_content(content)
    
    @abstractmethod
    def _parse_content(self, content: str) -> List[Dict]:
        pass

# dental_narrator/carriers/aetna.py  
class AetnaCarrier(BaseInsuranceCarrier):
    def get_carrier_name(self) -> str:
        return "Aetna"
    
    def get_base_urls(self) -> Dict[str, str]:
        return {
            "dental_policies": "https://www.aetna.com/health-care-professionals/provider-clinical-resources/dental-clinical-policy-bulletins.html",
            "coverage_guidelines": "https://www.aetna.com/health-care-professionals/clinical-policy-bulletins/dental-clinical-policy-bulletins.html",
            "prior_auth": "https://www.aetna.com/health-care-professionals/precertification-notification/precertification-lists.html"
        }
    
    def get_selectors(self) -> Dict[str, str]:
        return {
            "guidelines": ".policy-content",
            "title": ".policy-title",
            "effective_date": ".effective-date",
            "procedure_codes": ".procedure-code"
        }
    
    def _parse_content(self, content: str) -> List[Dict]:
        # Aetna-specific parsing logic
        # Extract policy details, procedure codes, coverage criteria
        pass

# Similar implementations for Cigna, MetLife, UHC...
```

### 3. MCP Server Implementation  
```python
# dental_narrator/mcp_server/server.py
from mcp import server, types
from mcp.server.fastapi import FastMCPServer
from crawl4ai import WebCrawler
from .carriers import get_carrier_crawler
from .vector_store import VectorStore
import asyncio

app = FastMCPServer("dental-crawl4ai")

@app.list_resources()
async def list_resources() -> list[types.Resource]:
    """List available insurance resources"""
    return [
        types.Resource(
            uri="dental://guidelines/aetna",
            name="Aetna Guidelines",
            mimeType="application/json",
            description="Current Aetna dental coverage guidelines"
        ),
        types.Resource(
            uri="dental://guidelines/cigna", 
            name="Cigna Guidelines",
            mimeType="application/json",
            description="Current Cigna dental coverage guidelines"
        ),
        # Additional carriers...
    ]

@app.read_resource()
async def read_resource(uri: str) -> str:
    """Read specific insurance resource"""
    parts = uri.split('/')
    
    if parts[2] == 'guidelines':
        carrier = parts[3]
        procedure = parts[4] if len(parts) > 4 else None
        
        crawler = get_carrier_crawler(carrier)
        guidelines = await crawler.crawl_guidelines([procedure] if procedure else None)
        
        return json.dumps({
            "carrier": carrier,
            "procedure": procedure,
            "guidelines": guidelines,
            "last_updated": datetime.utcnow().isoformat()
        })
    
    raise ValueError(f"Unknown resource: {uri}")

@app.list_tools()
async def list_tools() -> list[types.Tool]:
    """List available crawling tools"""
    return [
        types.Tool(
            name="crawl_carrier_updates",
            description="Crawl specific carrier for policy updates",
            inputSchema={
                "type": "object",
                "properties": {
                    "carrier": {"type": "string", "enum": ["aetna", "cigna", "metlife", "uhc"]},
                    "since": {"type": "string", "format": "date-time"},
                    "procedure_codes": {"type": "array", "items": {"type": "string"}}
                },
                "required": ["carrier"]
            }
        ),
        types.Tool(
            name="search_similar_policies",
            description="Vector search for similar policies",
            inputSchema={
                "type": "object", 
                "properties": {
                    "query_text": {"type": "string"},
                    "carriers": {"type": "array", "items": {"type": "string"}},
                    "similarity_threshold": {"type": "number", "minimum": 0, "maximum": 1}
                },
                "required": ["query_text"]
            }
        )
    ]

@app.call_tool()
async def call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    """Execute crawling tools"""
    if name == "crawl_carrier_updates":
        carrier = arguments["carrier"]
        since = arguments.get("since")
        procedure_codes = arguments.get("procedure_codes", [])
        
        crawler = get_carrier_crawler(carrier)
        updates = await crawler.crawl_guidelines(procedure_codes)
        
        # Store in vector database
        vector_store = VectorStore()
        await vector_store.store_guidelines(updates)
        
        return [types.TextContent(
            type="text",
            text=f"Found {len(updates)} updates for {carrier}. Stored in vector database."
        )]
    
    elif name == "search_similar_policies":
        query_text = arguments["query_text"]
        carriers = arguments.get("carriers", ["aetna", "cigna", "metlife", "uhc"])
        threshold = arguments.get("similarity_threshold", 0.7)
        
        vector_store = VectorStore()
        results = await vector_store.search_similar(query_text, carriers, threshold)
        
        return [types.TextContent(
            type="text", 
            text=json.dumps(results, indent=2)
        )]
    
    raise ValueError(f"Unknown tool: {name}")
```

---

## 🔄 **Change Detection System**

### Automated Policy Monitoring
```python
# dental_narrator/monitoring/change_detector.py
import hashlib
from typing import Dict, List
from datetime import datetime, timedelta

class PolicyChangeDetector:
    def __init__(self, vector_store, notification_service):
        self.vector_store = vector_store
        self.notifications = notification_service
        self.crawl_schedule = {
            "aetna": "0 2 * * *",      # Daily at 2 AM
            "cigna": "0 3 * * *",      # Daily at 3 AM  
            "metlife": "0 4 * * *",    # Daily at 4 AM
            "uhc": "0 5 * * *",        # Daily at 5 AM
        }
    
    async def detect_changes(self, carrier: str) -> List[Dict]:
        """Detect changes in carrier policies"""
        crawler = get_carrier_crawler(carrier)
        current_guidelines = await crawler.crawl_guidelines()
        
        changes = []
        for guideline in current_guidelines:
            # Generate content hash
            content_hash = hashlib.md5(guideline['content'].encode()).hexdigest()
            
            # Check against stored version
            stored = await self.vector_store.get_guideline(
                carrier, guideline['procedure_code']
            )
            
            if not stored or stored['content_hash'] != content_hash:
                change = {
                    'type': 'updated' if stored else 'new',
                    'carrier': carrier,
                    'procedure_code': guideline['procedure_code'],
                    'title': guideline['title'],
                    'old_hash': stored['content_hash'] if stored else None,
                    'new_hash': content_hash,
                    'detected_at': datetime.utcnow().isoformat(),
                    'guideline': guideline
                }
                changes.append(change)
                
                # Update vector store
                await self.vector_store.update_guideline(guideline, content_hash)
        
        if changes:
            await self.notifications.send_change_alert(carrier, changes)
        
        return changes
    
    async def run_scheduled_monitoring(self):
        """Run scheduled monitoring for all carriers"""
        for carrier in ["aetna", "cigna", "metlife", "uhc"]:
            try:
                changes = await self.detect_changes(carrier)
                print(f"{carrier}: {len(changes)} changes detected")
            except Exception as e:
                print(f"Error monitoring {carrier}: {e}")
                await self.notifications.send_error_alert(carrier, str(e))
```

---

## 📊 **Performance & Monitoring**

### Crawl Performance Metrics
```python
# dental_narrator/monitoring/metrics.py
class CrawlMetrics:
    def __init__(self, metrics_store):
        self.metrics = metrics_store
    
    async def track_crawl_performance(self, carrier: str, duration: float, 
                                     guidelines_count: int, errors: int = 0):
        """Track crawling performance metrics"""
        await self.metrics.record({
            'timestamp': datetime.utcnow().isoformat(),
            'carrier': carrier,
            'duration_seconds': duration,
            'guidelines_extracted': guidelines_count,
            'errors': errors,
            'success_rate': (guidelines_count / (guidelines_count + errors)) if (guidelines_count + errors) > 0 else 0
        })
    
    async def get_performance_dashboard(self, days: int = 7) -> Dict:
        """Get performance metrics for monitoring dashboard"""
        cutoff = (datetime.utcnow() - timedelta(days=days)).isoformat()
        
        metrics = await self.metrics.query({
            'timestamp': {'$gte': cutoff}
        })
        
        return {
            'total_crawls': len(metrics),
            'avg_duration': sum(m['duration_seconds'] for m in metrics) / len(metrics),
            'total_guidelines': sum(m['guidelines_extracted'] for m in metrics),
            'avg_success_rate': sum(m['success_rate'] for m in metrics) / len(metrics),
            'by_carrier': self._group_by_carrier(metrics)
        }
```

---

## ⚡ **Implementation Priority**

### Phase 1: Foundation (AOJ-74)
- 🔄 **Fork Crawl4AI repository** 
- 🔄 **Basic MCP server setup**
- 🔄 **Aetna carrier crawler (pilot)**
- 🔄 **Vector store integration**

### Phase 2: Expansion
- Additional carrier crawlers (Cigna, MetLife, UHC)
- Change detection system
- Performance monitoring
- Error handling and recovery

### Phase 3: Production
- Automated deployment pipeline
- Comprehensive monitoring dashboard
- Real-time alert system
- Performance optimization

---

## ✅ **Success Criteria**

### Technical KPIs
- **Crawl Success Rate**: 95%+ successful crawls
- **Data Freshness**: Updates within 24 hours of policy changes
- **MCP Response Time**: <2 seconds for tool calls
- **System Uptime**: 99.9% availability

### Business KPIs
- **Data Coverage**: 100% of target procedures for 4 carriers
- **Change Detection**: 90%+ of policy changes identified within 24 hours
- **Customer Impact**: 30%+ reduction in claim denials due to outdated data

---

*Crawl4AI MCP integration providing the foundational real-time data pipeline for Dental Narrator SaaS platform.*
