
# Prime Command: `/dn-context`

**Purpose**: Activate BMAD-Dental Narrator project context mode for AI assistant

## Command Execution Protocol

When `/dn-context` is invoked, the AI assistant should:

### 1. **Load Project Context**
- Access BMAD-Dental Narrator project documentation from Obsidian
- Review current Linear project status: [Dental Narrator Beta](https://linear.app/aojdevstudio/project/dental-narrator-beta-7b95ff168151)
- Check critical path issues: AOJ-74, AOJ-87, AOJ-88, AOJ-89
- Understand current phase: Foundation Setup → Vector Search Implementation

### 2. **Operating Mode Activation**
- **Meeting Notes**: Write to `BMAD - Dental Narrator/📝 Meeting Notes/` (NOT general responses)
- **Technical Updates**: Reference Linear issues and project status
- **Cross-Platform Awareness**: Maintain sync between Obsidian strategic docs and Linear technical implementation
- **Project Language**: Use established terminology (Custom GPT → SaaS transformation, 2,006 guidelines, vector search, etc.)

### 3. **Context Priorities**
1. **Current Critical Path**: AOJ-87 Vector Search Implementation (Due 2025-06-25)
2. **Foundational Blocker**: AOJ-74 Fork Crawl4AI Repository 
3. **Business Context**: Small dental practices (2-10 dentists), $149-999/month SaaS
4. **Technical Stack**: Crawl4AI MCP + Supabase + FastAPI + Next.js

### 4. **Response Format**
- Reference specific Linear issues when relevant
- Maintain project timeline awareness
- Use established folder structure for documentation
- Connect technical implementation to business objectives

### 5. **Key Project Knowledge**
- **Historic Success**: July 2024 KamDental implementation (300 → 33 aged claims)
- **Current Challenge**: Transform static guidelines to dynamic vector search
- **Target Market**: Small dental practices with claim narrative generation needs
- **Technology Evolution**: Custom GPT prototype → Scalable SaaS platform

## Usage Example
Got it! Here's the simple, portable prompt for the **Dental Narrator** project:

---
```
**Load the BMAD-Dental Narrator project context FROM OBSIDIAN:**

- Project: AI-powered dental insurance claim narrative generation (Custom GPT → SaaS)
- Status: Vector search implementation phase (AOJ-87 critical path)
- Management: Linear project "Dental Narrator Beta" (7b95ff168151)
- Goal: Transform 2,006 static guidelines into dynamic semantic search for 10,000+ dental practices

Read these Obsidian files for full context:

1. BMAD - Dental Narrator/README.md
2. BMAD - Dental Narrator/📈 Strategic Planning/Project Dental Narrator - Strategic Dashboard.md
3. BMAD - Dental Narrator/🏗️ Technical Documentation/System Architecture.md
4. BMAD - Dental Narrator/📝 Meeting Notes/2025-06-24 - Vector Search Development Session.md

```

---

Now you can copy/paste this into Claude Code, Cursor, or any other AI agent and they'll all read the same Obsidian files to get synchronized on where we are with the Dental Narrator project! 🚀

---

*Prime Command created: 2025-06-24*  
*Purpose: Ensure AI assistant understands BMAD-Dental Narrator project context and operating procedures*